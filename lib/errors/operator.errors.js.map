{"version": 3, "file": "operator.errors.js", "sourceRoot": "", "sources": ["../../src/errors/operator.errors.ts"], "names": [], "mappings": ";;;AAuCA,oDAiFC;AAxHD,qEAAwG;AACxG,kFAK+C;AAG/C,MAAa,YAAa,SAAQ,gCAAO;IACrC,YAAY,OAAO,GAAG,eAAe;QACjC,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,oCAAW,CAAC,KAAK,CAAC,CAAC;IACjD,CAAC;CACJ;AAJD,oCAIC;AAED,MAAa,uBAAwB,SAAQ,gCAAO;IAChD,YAAY,OAAO,GAAG,uBAAuB;QACzC,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,oCAAW,CAAC,IAAI,CAAC,CAAC;IAChD,CAAC;CACJ;AAJD,0DAIC;AAED,MAAa,wBAAyB,SAAQ,gCAAO;IACjD,YAAY,OAAO,GAAG,sBAAsB;QACxC,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,oCAAW,CAAC,IAAI,CAAC,CAAC;IAChD,CAAC;CACJ;AAJD,4DAIC;AAED,MAAa,mBAAoB,SAAQ,gCAAO;IAC5C,YAAY,OAAO,GAAG,uBAAuB;QACzC,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,oCAAW,CAAC,IAAI,CAAC,CAAC;IAChD,CAAC;CACJ;AAJD,kDAIC;AAED,MAAa,eAAgB,SAAQ,gCAAO;IACxC,YAAY,OAAO,GAAG,kBAAkB;QACpC,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,oCAAW,CAAC,IAAI,CAAC,CAAC;IAChD,CAAC;CACJ;AAJD,0CAIC;AAED,SAAgB,oBAAoB,CAChC,EAAE,IAAI,EAAE,MAAM,EAAiB;IAE/B,QAAQ,IAAI,EAAE,CAAC;QACX,KAAK,sCAAkB,CAAC,OAAO;YAC3B,OAAO,IAAI,YAAY,CAAC,0CAA0C,CAAC,CAAC;QAExE,KAAK,sCAAkB,CAAC,aAAa,CAAC;QACtC,KAAK,sCAAkB,CAAC,oBAAoB,CAAC;QAC7C,KAAK,sCAAkB,CAAC,iBAAiB;YACrC,OAAO,IAAI,YAAY,CAAC,MAAM,IAAI,eAAe,CAAC,CAAC;QAEvD,KAAK,sCAAkB,CAAC,oBAAoB;YACxC,OAAO,IAAI,uBAAuB,CAAC,qCAAqC,CAAC,CAAC;QAE9E,KAAK,sCAAkB,CAAC,cAAc;YAClC,OAAO,IAAI,uBAAuB,CAAC,mBAAmB,CAAC,CAAC;QAE5D,KAAK,sCAAkB,CAAC,uBAAuB;YAC3C,OAAO,IAAI,uBAAuB,CAAC,wCAAwC,CAAC,CAAC;QAEjF,KAAK,sCAAkB,CAAC,uBAAuB;YAC3C,OAAO,IAAI,eAAe,CAAC,yBAAyB,CAAC,CAAC;QAE1D,KAAK,sCAAkB,CAAC,4BAA4B;YAChD,OAAO,IAAI,eAAe,CAAC,8BAA8B,CAAC,CAAC;QAE/D,KAAK,sCAAkB,CAAC,oBAAoB,CAAC;QAC7C,KAAK,sCAAkB,CAAC,qBAAqB;YACzC,OAAO,IAAI,mBAAmB,CAAC,MAAM,CAAC,CAAC;QAE3C,KAAK,sCAAkB,CAAC,eAAe,CAAC;QACxC,KAAK,sCAAkB,CAAC,mBAAmB;YACvC,OAAO,IAAI,8BAA8B,CAAC,MAAM,CAAC,CAAC;QAEtD,KAAK,sCAAkB,CAAC,qBAAqB;YACzC,OAAO,IAAI,uBAAuB,CAAC,MAAM,IAAI,uBAAuB,CAAC,CAAC;QAE1E,KAAK,sCAAkB,CAAC,cAAc,CAAC;QACvC,KAAK,sCAAkB,CAAC,YAAY;YAChC,OAAO,IAAI,yBAAyB,CAAC,MAAM,CAAC,CAAC;QAEjD,KAAK,sCAAkB,CAAC,iBAAiB,CAAC;QAC1C,KAAK,sCAAkB,CAAC,kBAAkB,CAAC;QAC3C,KAAK,sCAAkB,CAAC,qBAAqB,CAAC;QAC9C,KAAK,sCAAkB,CAAC,oBAAoB;YACxC,OAAO,IAAI,yBAAyB,CAAC,MAAM,CAAC,CAAC;QAEjD,KAAK,sCAAkB,CAAC,qBAAqB;YACzC,OAAO,IAAI,uBAAuB,CAAC,uBAAuB,CAAC,CAAC;QAEhE,KAAK,sCAAkB,CAAC,eAAe;YACnC,OAAO,IAAI,uBAAuB,CAAC,MAAM,CAAC,CAAC;QAE/C,KAAK,sCAAkB,CAAC,kBAAkB;YACtC,OAAO,IAAI,6BAA6B,CAAC,MAAM,CAAC,CAAC;QAErD,KAAK,sCAAkB,CAAC,gBAAgB;YACpC,OAAO,IAAI,eAAe,CAAC,kBAAkB,CAAC,CAAC;QAEnD,KAAK,sCAAkB,CAAC,kBAAkB;YACtC,OAAO,IAAI,wBAAwB,CAAC,MAAM,IAAI,oBAAoB,CAAC,CAAC;QAExE,KAAK,sCAAkB,CAAC,gBAAgB;YACpC,OAAO,IAAI,4BAA4B,CAAC,MAAM,CAAC,CAAC;QAEpD,KAAK,sCAAkB,CAAC,sBAAsB;YAC1C,OAAO,IAAI,eAAe,CAAC,wBAAwB,CAAC,CAAC;QAEzD,KAAK,sCAAkB,CAAC,eAAe,CAAC;QACxC,KAAK,sCAAkB,CAAC,aAAa,CAAC;QACtC,KAAK,sCAAkB,CAAC,aAAa;YACjC,OAAO,IAAI,yBAAyB,CAAC,MAAM,CAAC,CAAC;QAEjD,KAAK,sCAAkB,CAAC,gBAAgB,CAAC;QACzC,KAAK,sCAAkB,CAAC,mBAAmB;YACvC,OAAO,IAAI,eAAe,CAAC,MAAM,IAAI,gBAAgB,CAAC,CAAC;QAE3D;YACI,OAAO,IAAI,YAAY,CAAC,MAAM,IAAI,2BAA2B,IAAI,EAAE,CAAC,CAAC;IAC7E,CAAC;AACL,CAAC;AAGD,MAAa,8BAA+B,SAAQ,gCAAO;IACvD,YAAY,OAAO,GAAG,qBAAqB;QACvC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,oCAAW,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;CACJ;AAJD,wEAIC;AAED,MAAa,yBAA0B,SAAQ,gCAAO;IAClD,YAAY,OAAO,GAAG,gBAAgB;QAClC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,oCAAW,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;CACJ;AAJD,8DAIC;AAED,MAAa,yBAA0B,SAAQ,gCAAO;IAClD,YAAY,OAAO,GAAG,eAAe;QACjC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,oCAAW,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;CACJ;AAJD,8DAIC;AAED,MAAa,uBAAwB,SAAQ,gCAAO;IAChD,YAAY,OAAO,GAAG,0BAA0B;QAC5C,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,oCAAW,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;CACJ;AAJD,0DAIC;AAED,MAAa,6BAA8B,SAAQ,gCAAO;IACtD,YAAY,OAAO,GAAG,oBAAoB;QACtC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,oCAAW,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;CACJ;AAJD,sEAIC;AAED,MAAa,4BAA6B,SAAQ,gCAAO;IACrD,YAAY,OAAO,GAAG,qBAAqB;QACvC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,oCAAW,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;CACJ;AAJD,oEAIC;AAED,MAAa,yBAA0B,SAAQ,gCAAO;IAClD,YAAY,OAAO,GAAG,0BAA0B;QAC5C,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,oCAAW,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;CACJ;AAJD,8DAIC;AAED,MAAa,2BAA4B,SAAQ,gCAAO;IACpD,YAAY,OAAO,GAAG,wBAAwB;QAC1C,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,oCAAW,CAAC,KAAK,CAAC,CAAC;IAClD,CAAC;CACJ;AAJD,kEAIC;AAED,MAAa,2BAA4B,SAAQ,gCAAO;IACpD,YAAY,OAAO,GAAG,wBAAwB;QAC1C,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,oCAAW,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;CACJ;AAJD,kEAIC;AAGM,MAAM,yBAAyB,GAAG,GAAkB,EAAE,CAAC,CAAC;IAC3D,IAAI,EAAE,sCAAkB,CAAC,kBAAkB;IAC3C,MAAM,EAAE,0CAAsB,CAAC,sCAAkB,CAAC,kBAAkB,CAAC;CACxE,CAAC,CAAC;AAHU,QAAA,yBAAyB,6BAGnC;AAEI,MAAM,oBAAoB,GAAG,GAAkB,EAAE,CAAC,CAAC;IACtD,IAAI,EAAE,sCAAkB,CAAC,aAAa;IACtC,MAAM,EAAE,0CAAsB,CAAC,sCAAkB,CAAC,aAAa,CAAC;CACnE,CAAC,CAAC;AAHU,QAAA,oBAAoB,wBAG9B;AAEI,MAAM,4BAA4B,GAAG,GAAkB,EAAE,CAAC,CAAC;IAC9D,IAAI,EAAE,sCAAkB,CAAC,qBAAqB;IAC9C,MAAM,EAAE,0CAAsB,CAAC,sCAAkB,CAAC,qBAAqB,CAAC;CAC3E,CAAC,CAAC;AAHU,QAAA,4BAA4B,gCAGtC;AAEI,MAAM,oBAAoB,GAAG,GAAkB,EAAE,CAAC,CAAC;IACtD,IAAI,EAAE,sCAAkB,CAAC,cAAc;IACvC,MAAM,EAAE,0CAAsB,CAAC,sCAAkB,CAAC,cAAc,CAAC;CACpE,CAAC,CAAC;AAHU,QAAA,oBAAoB,wBAG9B;AAEI,MAAM,yBAAyB,GAAG,GAAkB,EAAE,CAAC,CAAC;IAC3D,IAAI,EAAE,sCAAkB,CAAC,mBAAmB;IAC5C,MAAM,EAAE,0CAAsB,CAAC,sCAAkB,CAAC,mBAAmB,CAAC;CACzE,CAAC,CAAC;AAHU,QAAA,yBAAyB,6BAGnC;AAEI,MAAM,uBAAuB,GAAG,GAAkB,EAAE,CAAC,CAAC;IACzD,IAAI,EAAE,sCAAkB,CAAC,gBAAgB;IACzC,MAAM,EAAE,0CAAsB,CAAC,sCAAkB,CAAC,gBAAgB,CAAC;CACtE,CAAC,CAAC;AAHU,QAAA,uBAAuB,2BAGjC;AAEI,MAAM,2BAA2B,GAAG,GAAkB,EAAE,CAAC,CAAC;IAC7D,IAAI,EAAE,sCAAkB,CAAC,oBAAoB;IAC7C,MAAM,EAAE,0CAAsB,CAAC,sCAAkB,CAAC,oBAAoB,CAAC;CAC1E,CAAC,CAAC;AAHU,QAAA,2BAA2B,+BAGrC;AAGI,MAAM,cAAc,GAAG,CAAC,GAAY,EAAW,EAAE,CACpD,GAAG,YAAY,wCAAe;IAC9B,GAAG,YAAY,YAAY;IAC3B,GAAG,YAAY,gDAAuB,CAAC;AAH9B,QAAA,cAAc,kBAGgB;AAEpC,MAAM,iBAAiB,GAAG,CAAC,GAAY,EAAW,EAAE,CACvD,GAAG,YAAY,wCAAe;IAC9B,GAAG,YAAY,YAAY;IAC3B,GAAG,YAAY,uBAAuB;IACtC,GAAG,YAAY,yBAAyB;IACxC,GAAG,YAAY,4BAA4B,CAAC;AALnC,QAAA,iBAAiB,qBAKkB;AAEzC,MAAM,eAAe,GAAG,CAAC,GAAY,EAAW,EAAE,CACrD,GAAG,YAAY,yBAAyB;IACxC,GAAG,YAAY,6BAA6B;IAC5C,GAAG,YAAY,uBAAuB,CAAC;AAH9B,QAAA,eAAe,mBAGe"}