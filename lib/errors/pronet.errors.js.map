{"version": 3, "file": "pronet.errors.js", "sourceRoot": "", "sources": ["../../src/errors/pronet.errors.ts"], "names": [], "mappings": ";;;AAqOA,0DAiEC;AAED,0CAQC;AAED,8CAUC;AA5TD,MAAa,eAAgB,SAAQ,KAAK;IACtC,YAAY,OAAe;QACvB,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC;IAClC,CAAC;CACJ;AALD,0CAKC;AAED,MAAa,SAAU,SAAQ,KAAK;IAIhC,YAAY,IAAY,EAAE,MAAc,EAAE,OAAgB;QACtD,KAAK,CAAC,OAAO,IAAI,MAAM,CAAC,CAAC;QACzB,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;QACxB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACzB,CAAC;CACJ;AAVD,8BAUC;AAED,MAAa,uBAAwB,SAAQ,SAAS;IAClD,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,KAAK,EAAE,uBAAuB,EAAE,OAAO,CAAC,CAAC;QAChD,IAAI,CAAC,IAAI,GAAG,yBAAyB,CAAC;IAC1C,CAAC;CACJ;AALD,0DAKC;AAED,MAAa,0BAA2B,SAAQ,SAAS;IACrD,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,KAAK,EAAE,qBAAqB,EAAE,OAAO,CAAC,CAAC;QAC9C,IAAI,CAAC,IAAI,GAAG,4BAA4B,CAAC;IAC7C,CAAC;CACJ;AALD,gEAKC;AAED,MAAa,sBAAuB,SAAQ,SAAS;IACjD,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,KAAK,EAAE,iBAAiB,EAAE,OAAO,CAAC,CAAC;QAC1C,IAAI,CAAC,IAAI,GAAG,wBAAwB,CAAC;IACzC,CAAC;CACJ;AALD,wDAKC;AAED,MAAa,0BAA2B,SAAQ,SAAS;IACrD,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,KAAK,EAAE,sBAAsB,EAAE,OAAO,CAAC,CAAC;QAC/C,IAAI,CAAC,IAAI,GAAG,4BAA4B,CAAC;IAC7C,CAAC;CACJ;AALD,gEAKC;AAED,MAAa,0BAA2B,SAAQ,SAAS;IACrD,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,KAAK,EAAE,oBAAoB,EAAE,OAAO,CAAC,CAAC;QAC7C,IAAI,CAAC,IAAI,GAAG,4BAA4B,CAAC;IAC7C,CAAC;CACJ;AALD,gEAKC;AAED,MAAa,qBAAsB,SAAQ,SAAS;IAChD,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,KAAK,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC;QACxC,IAAI,CAAC,IAAI,GAAG,uBAAuB,CAAC;IACxC,CAAC;CACJ;AALD,sDAKC;AAED,MAAa,qBAAsB,SAAQ,SAAS;IAChD,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,KAAK,EAAE,eAAe,EAAE,OAAO,CAAC,CAAC;QACxC,IAAI,CAAC,IAAI,GAAG,uBAAuB,CAAC;IACxC,CAAC;CACJ;AALD,sDAKC;AAED,MAAa,sBAAuB,SAAQ,SAAS;IACjD,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,KAAK,EAAE,iBAAiB,EAAE,OAAO,CAAC,CAAC;QAC1C,IAAI,CAAC,IAAI,GAAG,wBAAwB,CAAC;IACzC,CAAC;CACJ;AALD,wDAKC;AAED,MAAa,yBAA0B,SAAQ,SAAS;IACpD,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,KAAK,EAAE,oBAAoB,EAAE,OAAO,CAAC,CAAC;QAC7C,IAAI,CAAC,IAAI,GAAG,2BAA2B,CAAC;IAC5C,CAAC;CACJ;AALD,8DAKC;AAED,MAAa,qBAAsB,SAAQ,SAAS;IAChD,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,KAAK,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAC;QACzC,IAAI,CAAC,IAAI,GAAG,uBAAuB,CAAC;IACxC,CAAC;CACJ;AALD,sDAKC;AAED,MAAa,wBAAyB,SAAQ,SAAS;IACnD,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,KAAK,EAAE,kBAAkB,EAAE,OAAO,CAAC,CAAC;QAC3C,IAAI,CAAC,IAAI,GAAG,0BAA0B,CAAC;IAC3C,CAAC;CACJ;AALD,4DAKC;AAED,MAAa,wBAAyB,SAAQ,SAAS;IACnD,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,KAAK,EAAE,mBAAmB,EAAE,OAAO,CAAC,CAAC;QAC5C,IAAI,CAAC,IAAI,GAAG,0BAA0B,CAAC;IAC3C,CAAC;CACJ;AALD,4DAKC;AAED,MAAa,yBAA0B,SAAQ,SAAS;IACpD,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,KAAK,EAAE,oBAAoB,EAAE,OAAO,CAAC,CAAC;QAC7C,IAAI,CAAC,IAAI,GAAG,2BAA2B,CAAC;IAC5C,CAAC;CACJ;AALD,8DAKC;AAED,MAAa,4BAA6B,SAAQ,SAAS;IACvD,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,KAAK,EAAE,uBAAuB,EAAE,OAAO,CAAC,CAAC;QAChD,IAAI,CAAC,IAAI,GAAG,8BAA8B,CAAC;IAC/C,CAAC;CACJ;AALD,oEAKC;AAED,MAAa,2BAA4B,SAAQ,SAAS;IACtD,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,KAAK,EAAE,sBAAsB,EAAE,OAAO,CAAC,CAAC;QAC/C,IAAI,CAAC,IAAI,GAAG,6BAA6B,CAAC;IAC9C,CAAC;CACJ;AALD,kEAKC;AAED,MAAa,4BAA6B,SAAQ,SAAS;IACvD,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,EAAE,sBAAsB,EAAE,OAAO,CAAC,CAAC;QAC1C,IAAI,CAAC,IAAI,GAAG,8BAA8B,CAAC;IAC/C,CAAC;CACJ;AALD,oEAKC;AAED,MAAa,sBAAuB,SAAQ,SAAS;IACjD,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAC;QACpC,IAAI,CAAC,IAAI,GAAG,wBAAwB,CAAC;IACzC,CAAC;CACJ;AALD,wDAKC;AAED,MAAa,8BAA+B,SAAQ,SAAS;IACzD,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,EAAE,yBAAyB,EAAE,OAAO,CAAC,CAAC;QAC7C,IAAI,CAAC,IAAI,GAAG,gCAAgC,CAAC;IACjD,CAAC;CACJ;AALD,wEAKC;AAED,MAAa,8BAA+B,SAAQ,SAAS;IACzD,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,EAAE,yBAAyB,EAAE,OAAO,CAAC,CAAC;QAC7C,IAAI,CAAC,IAAI,GAAG,gCAAgC,CAAC;IACjD,CAAC;CACJ;AALD,wEAKC;AAED,MAAa,sBAAuB,SAAQ,SAAS;IACjD,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,EAAE,sBAAsB,EAAE,OAAO,CAAC,CAAC;QAC1C,IAAI,CAAC,IAAI,GAAG,wBAAwB,CAAC;IACzC,CAAC;CACJ;AALD,wDAKC;AAED,MAAa,kCAAmC,SAAQ,SAAS;IAC7D,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,EAAE,8BAA8B,EAAE,OAAO,CAAC,CAAC;QAClD,IAAI,CAAC,IAAI,GAAG,oCAAoC,CAAC;IACrD,CAAC;CACJ;AALD,gFAKC;AAED,MAAa,4BAA6B,SAAQ,SAAS;IACvD,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,KAAK,EAAE,uBAAuB,EAAE,OAAO,CAAC,CAAC;QAChD,IAAI,CAAC,IAAI,GAAG,8BAA8B,CAAC;IAC/C,CAAC;CACJ;AALD,oEAKC;AAED,MAAa,wBAAyB,SAAQ,SAAS;IACnD,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,KAAK,EAAE,kBAAkB,EAAE,OAAO,CAAC,CAAC;QAC3C,IAAI,CAAC,IAAI,GAAG,0BAA0B,CAAC;IAC3C,CAAC;CACJ;AALD,4DAKC;AAED,MAAa,6BAA8B,SAAQ,SAAS;IACxD,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,KAAK,EAAE,wBAAwB,EAAE,OAAO,CAAC,CAAC;QACjD,IAAI,CAAC,IAAI,GAAG,+BAA+B,CAAC;IAChD,CAAC;CACJ;AALD,sEAKC;AAED,MAAa,uBAAwB,SAAQ,SAAS;IAClD,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,KAAK,EAAE,mBAAmB,EAAE,OAAO,CAAC,CAAC;QAC5C,IAAI,CAAC,IAAI,GAAG,yBAAyB,CAAC;IAC1C,CAAC;CACJ;AALD,0DAKC;AAED,MAAa,wBAAyB,SAAQ,SAAS;IACnD,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,KAAK,EAAE,kBAAkB,EAAE,OAAO,CAAC,CAAC;QAC3C,IAAI,CAAC,IAAI,GAAG,0BAA0B,CAAC;IAC3C,CAAC;CACJ;AALD,4DAKC;AAED,MAAa,2BAA4B,SAAQ,SAAS;IACtD,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,KAAK,EAAE,qBAAqB,EAAE,OAAO,CAAC,CAAC;QAC9C,IAAI,CAAC,IAAI,GAAG,6BAA6B,CAAC;IAC9C,CAAC;CACJ;AALD,kEAKC;AAED,MAAa,qBAAsB,SAAQ,SAAS;IAChD,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,KAAK,EAAE,iBAAiB,EAAE,OAAO,CAAC,CAAC;QAC1C,IAAI,CAAC,IAAI,GAAG,uBAAuB,CAAC;IACxC,CAAC;CACJ;AALD,sDAKC;AAED,MAAa,oBAAqB,SAAQ,SAAS;IAC/C,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,KAAK,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;QACvC,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC;IACvC,CAAC;CACJ;AALD,oDAKC;AAED,MAAa,4BAA6B,SAAQ,SAAS;IACvD,YAAY,OAAgB;QACxB,KAAK,CAAC,CAAC,KAAK,EAAE,uBAAuB,EAAE,OAAO,CAAC,CAAC;QAChD,IAAI,CAAC,IAAI,GAAG,8BAA8B,CAAC;IAC/C,CAAC;CACJ;AALD,oEAKC;AAED,SAAgB,uBAAuB,CAAC,IAAY,EAAE,MAAc,EAAE,OAAgB;IAClF,QAAQ,IAAI,EAAE,CAAC;QACX,KAAK,CAAC,KAAK;YACP,OAAO,IAAI,uBAAuB,CAAC,OAAO,CAAC,CAAC;QAChD,KAAK,CAAC,KAAK;YACP,OAAO,IAAI,0BAA0B,CAAC,OAAO,CAAC,CAAC;QACnD,KAAK,CAAC,KAAK;YACP,OAAO,IAAI,sBAAsB,CAAC,OAAO,CAAC,CAAC;QAC/C,KAAK,CAAC,KAAK;YACP,OAAO,IAAI,0BAA0B,CAAC,OAAO,CAAC,CAAC;QACnD,KAAK,CAAC,KAAK;YACP,OAAO,IAAI,0BAA0B,CAAC,OAAO,CAAC,CAAC;QACnD,KAAK,CAAC,KAAK;YACP,OAAO,IAAI,qBAAqB,CAAC,OAAO,CAAC,CAAC;QAC9C,KAAK,CAAC,KAAK;YACP,OAAO,IAAI,qBAAqB,CAAC,OAAO,CAAC,CAAC;QAC9C,KAAK,CAAC,KAAK;YACP,OAAO,IAAI,sBAAsB,CAAC,OAAO,CAAC,CAAC;QAC/C,KAAK,CAAC,KAAK;YACP,OAAO,IAAI,yBAAyB,CAAC,OAAO,CAAC,CAAC;QAClD,KAAK,CAAC,KAAK;YACP,OAAO,IAAI,qBAAqB,CAAC,OAAO,CAAC,CAAC;QAC9C,KAAK,CAAC,KAAK;YACP,OAAO,IAAI,wBAAwB,CAAC,OAAO,CAAC,CAAC;QACjD,KAAK,CAAC,KAAK;YACP,OAAO,IAAI,wBAAwB,CAAC,OAAO,CAAC,CAAC;QACjD,KAAK,CAAC,KAAK;YACP,OAAO,IAAI,yBAAyB,CAAC,OAAO,CAAC,CAAC;QAClD,KAAK,CAAC,KAAK;YACP,OAAO,IAAI,4BAA4B,CAAC,OAAO,CAAC,CAAC;QACrD,KAAK,CAAC,KAAK;YACP,OAAO,IAAI,2BAA2B,CAAC,OAAO,CAAC,CAAC;QACpD,KAAK,CAAC;YACF,OAAO,IAAI,4BAA4B,CAAC,OAAO,CAAC,CAAC;QACrD,KAAK,CAAC;YACF,OAAO,IAAI,sBAAsB,CAAC,OAAO,CAAC,CAAC;QAC/C,KAAK,CAAC;YACF,OAAO,IAAI,8BAA8B,CAAC,OAAO,CAAC,CAAC;QACvD,KAAK,CAAC;YACF,OAAO,IAAI,8BAA8B,CAAC,OAAO,CAAC,CAAC;QACvD,KAAK,CAAC;YACF,OAAO,IAAI,sBAAsB,CAAC,OAAO,CAAC,CAAC;QAC/C,KAAK,CAAC;YACF,OAAO,IAAI,kCAAkC,CAAC,OAAO,CAAC,CAAC;QAC3D,KAAK,CAAC,KAAK;YACP,OAAO,IAAI,4BAA4B,CAAC,OAAO,CAAC,CAAC;QACrD,KAAK,CAAC,KAAK;YACP,OAAO,IAAI,wBAAwB,CAAC,OAAO,CAAC,CAAC;QACjD,KAAK,CAAC,KAAK;YACP,OAAO,IAAI,6BAA6B,CAAC,OAAO,CAAC,CAAC;QACtD,KAAK,CAAC,KAAK;YACP,OAAO,IAAI,uBAAuB,CAAC,OAAO,CAAC,CAAC;QAChD,KAAK,CAAC,KAAK;YACP,OAAO,IAAI,wBAAwB,CAAC,OAAO,CAAC,CAAC;QACjD,KAAK,CAAC,KAAK;YACP,OAAO,IAAI,2BAA2B,CAAC,OAAO,CAAC,CAAC;QACpD,KAAK,CAAC,KAAK;YACP,OAAO,IAAI,qBAAqB,CAAC,OAAO,CAAC,CAAC;QAC9C,KAAK,CAAC,KAAK;YACP,OAAO,IAAI,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAC7C,KAAK,CAAC,KAAK;YACP,OAAO,IAAI,4BAA4B,CAAC,OAAO,CAAC,CAAC;QACrD;YACI,OAAO,IAAI,SAAS,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;IACpD,CAAC;AACL,CAAC;AAED,SAAgB,eAAe,CAAC,GAAQ;IACpC,OAAO,GAAG,YAAY,0BAA0B;QACzC,GAAG,YAAY,wBAAwB;QACvC,GAAG,YAAY,yBAAyB;QACxC,GAAG,YAAY,4BAA4B;QAC3C,GAAG,YAAY,2BAA2B;QAC1C,GAAG,YAAY,wBAAwB;QACvC,GAAG,YAAY,qBAAqB,CAAC;AAChD,CAAC;AAED,SAAgB,iBAAiB,CAAC,GAAQ;IACtC,OAAO,GAAG,YAAY,qBAAqB;QACpC,GAAG,YAAY,qBAAqB;QACpC,GAAG,YAAY,sBAAsB;QACrC,GAAG,YAAY,yBAAyB;QACxC,GAAG,YAAY,qBAAqB;QACpC,GAAG,YAAY,oBAAoB;QACnC,GAAG,YAAY,4BAA4B;QAC3C,GAAG,YAAY,uBAAuB;QACtC,GAAG,YAAY,sBAAsB,CAAC;AACjD,CAAC"}