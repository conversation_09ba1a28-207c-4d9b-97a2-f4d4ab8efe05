"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.refundCondition = exports.rollbackCondition = exports.retryCondition = exports.operatorUnauthorizedRequest = exports.operatorPlayerSuspended = exports.operatorBetAlreadySettled = exports.operatorGameNotFound = exports.operatorAuthenticationFailed = exports.operatorTokenExpired = exports.operatorInsufficientFunds = exports.OperatorInvalidRequestError = exports.OperatorHashValidationError = exports.OperatorTokenExpiredError = exports.OperatorPlayerSuspendedError = exports.OperatorCustomerNotFoundError = exports.OperatorBettingOffError = exports.OperatorLimitReachedError = exports.OperatorGameNotFoundError = exports.OperatorBetAlreadySettledError = exports.ValidationError = exports.TransactionNotFound = exports.InsufficientBalanceError = exports.AuthenticateFailedError = exports.GeneralError = void 0;
exports.mapOperatorToSWError = mapOperatorToSWError;
const operator_entities_1 = require("../entities/operator.entities");
const sw_wallet_adapter_core_1 = require("@skywind-group/sw-wallet-adapter-core");
class GeneralError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "General error") {
        super(500, 5000, message, sw_wallet_adapter_core_1.ERROR_LEVEL.ERROR);
    }
}
exports.GeneralError = GeneralError;
class AuthenticateFailedError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Authentication failed") {
        super(401, 4010, message, sw_wallet_adapter_core_1.ERROR_LEVEL.WARN);
    }
}
exports.AuthenticateFailedError = AuthenticateFailedError;
class InsufficientBalanceError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Insufficient balance") {
        super(400, 4001, message, sw_wallet_adapter_core_1.ERROR_LEVEL.WARN);
    }
}
exports.InsufficientBalanceError = InsufficientBalanceError;
class TransactionNotFound extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Transaction not found") {
        super(404, 4040, message, sw_wallet_adapter_core_1.ERROR_LEVEL.WARN);
    }
}
exports.TransactionNotFound = TransactionNotFound;
class ValidationError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Validation error") {
        super(400, 4000, message, sw_wallet_adapter_core_1.ERROR_LEVEL.WARN);
    }
}
exports.ValidationError = ValidationError;
function mapOperatorToSWError({ code, status }) {
    switch (code) {
        case operator_entities_1.operatorErrorCodes.SUCCESS:
            return new GeneralError("Unexpected success code in error handler");
        case operator_entities_1.operatorErrorCodes.UNKNOWN_ERROR:
        case operator_entities_1.operatorErrorCodes.INTERNAL_CACHE_ERROR:
        case operator_entities_1.operatorErrorCodes.DATA_OUT_OF_RANGE:
            return new GeneralError(status || "Unknown error");
        case operator_entities_1.operatorErrorCodes.UNAUTHORIZED_REQUEST:
            return new AuthenticateFailedError("Unauthorized request - invalid hash");
        case operator_entities_1.operatorErrorCodes.NOT_INTEGRATED:
            return new AuthenticateFailedError("Vendor not active");
        case operator_entities_1.operatorErrorCodes.TOKEN_CUSTOMER_MISMATCH:
            return new AuthenticateFailedError("Token was created for another customer");
        case operator_entities_1.operatorErrorCodes.UNSUPPORTED_API_VERSION:
            return new ValidationError("Unsupported API version");
        case operator_entities_1.operatorErrorCodes.PROMOTION_TYPE_NOT_SUPPORTED:
            return new ValidationError("Promotion type not supported");
        case operator_entities_1.operatorErrorCodes.BET_RECORD_NOT_FOUND:
        case operator_entities_1.operatorErrorCodes.TRANSACTION_NOT_FOUND:
            return new TransactionNotFound(status);
        case operator_entities_1.operatorErrorCodes.BET_ALREADY_WON:
        case operator_entities_1.operatorErrorCodes.BET_ALREADY_SETTLED:
            return new OperatorBetAlreadySettledError(status);
        case operator_entities_1.operatorErrorCodes.AUTHENTICATION_FAILED:
            return new AuthenticateFailedError(status || "Authentication failed");
        case operator_entities_1.operatorErrorCodes.GAME_NOT_FOUND:
        case operator_entities_1.operatorErrorCodes.INVALID_GAME:
            return new OperatorGameNotFoundError(status);
        case operator_entities_1.operatorErrorCodes.BET_LIMIT_REACHED:
        case operator_entities_1.operatorErrorCodes.LOSS_LIMIT_REACHED:
        case operator_entities_1.operatorErrorCodes.SESSION_LIMIT_REACHED:
        case operator_entities_1.operatorErrorCodes.PROFIT_LIMIT_REACHED:
            return new OperatorLimitReachedError(status);
        case operator_entities_1.operatorErrorCodes.INVALID_CASINO_VENDOR:
            return new AuthenticateFailedError("Invalid casino vendor");
        case operator_entities_1.operatorErrorCodes.ALL_BET_ARE_OFF:
            return new OperatorBettingOffError(status);
        case operator_entities_1.operatorErrorCodes.CUSTOMER_NOT_FOUND:
            return new OperatorCustomerNotFoundError(status);
        case operator_entities_1.operatorErrorCodes.INVALID_CURRENCY:
            return new ValidationError("Invalid currency");
        case operator_entities_1.operatorErrorCodes.INSUFFICIENT_FUNDS:
            return new InsufficientBalanceError(status || "Insufficient funds");
        case operator_entities_1.operatorErrorCodes.PLAYER_SUSPENDED:
            return new OperatorPlayerSuspendedError(status);
        case operator_entities_1.operatorErrorCodes.REQUIRED_FIELD_MISSING:
            return new ValidationError("Required field missing");
        case operator_entities_1.operatorErrorCodes.TOKEN_NOT_FOUND:
        case operator_entities_1.operatorErrorCodes.TOKEN_TIMEOUT:
        case operator_entities_1.operatorErrorCodes.TOKEN_INVALID:
            return new OperatorTokenExpiredError(status);
        case operator_entities_1.operatorErrorCodes.NEGATIVE_DEPOSIT:
        case operator_entities_1.operatorErrorCodes.NEGATIVE_WITHDRAWAL:
            return new ValidationError(status || "Invalid amount");
        default:
            return new GeneralError(status || `Unknown operator error: ${code}`);
    }
}
class OperatorBetAlreadySettledError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Bet already settled") {
        super(400, 40001, message, sw_wallet_adapter_core_1.ERROR_LEVEL.WARN);
    }
}
exports.OperatorBetAlreadySettledError = OperatorBetAlreadySettledError;
class OperatorGameNotFoundError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Game not found") {
        super(404, 40401, message, sw_wallet_adapter_core_1.ERROR_LEVEL.WARN);
    }
}
exports.OperatorGameNotFoundError = OperatorGameNotFoundError;
class OperatorLimitReachedError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Limit reached") {
        super(403, 40301, message, sw_wallet_adapter_core_1.ERROR_LEVEL.WARN);
    }
}
exports.OperatorLimitReachedError = OperatorLimitReachedError;
class OperatorBettingOffError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Betting is currently off") {
        super(503, 50301, message, sw_wallet_adapter_core_1.ERROR_LEVEL.WARN);
    }
}
exports.OperatorBettingOffError = OperatorBettingOffError;
class OperatorCustomerNotFoundError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Customer not found") {
        super(404, 40402, message, sw_wallet_adapter_core_1.ERROR_LEVEL.WARN);
    }
}
exports.OperatorCustomerNotFoundError = OperatorCustomerNotFoundError;
class OperatorPlayerSuspendedError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Player is suspended") {
        super(403, 40302, message, sw_wallet_adapter_core_1.ERROR_LEVEL.WARN);
    }
}
exports.OperatorPlayerSuspendedError = OperatorPlayerSuspendedError;
class OperatorTokenExpiredError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Token expired or invalid") {
        super(401, 40101, message, sw_wallet_adapter_core_1.ERROR_LEVEL.WARN);
    }
}
exports.OperatorTokenExpiredError = OperatorTokenExpiredError;
class OperatorHashValidationError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Hash validation failed") {
        super(401, 40102, message, sw_wallet_adapter_core_1.ERROR_LEVEL.ERROR);
    }
}
exports.OperatorHashValidationError = OperatorHashValidationError;
class OperatorInvalidRequestError extends sw_wallet_adapter_core_1.SWError {
    constructor(message = "Invalid request format") {
        super(400, 40002, message, sw_wallet_adapter_core_1.ERROR_LEVEL.WARN);
    }
}
exports.OperatorInvalidRequestError = OperatorInvalidRequestError;
const operatorInsufficientFunds = () => ({
    code: operator_entities_1.operatorErrorCodes.INSUFFICIENT_FUNDS,
    status: operator_entities_1.operatorStatusMessages[operator_entities_1.operatorErrorCodes.INSUFFICIENT_FUNDS]
});
exports.operatorInsufficientFunds = operatorInsufficientFunds;
const operatorTokenExpired = () => ({
    code: operator_entities_1.operatorErrorCodes.TOKEN_TIMEOUT,
    status: operator_entities_1.operatorStatusMessages[operator_entities_1.operatorErrorCodes.TOKEN_TIMEOUT]
});
exports.operatorTokenExpired = operatorTokenExpired;
const operatorAuthenticationFailed = () => ({
    code: operator_entities_1.operatorErrorCodes.AUTHENTICATION_FAILED,
    status: operator_entities_1.operatorStatusMessages[operator_entities_1.operatorErrorCodes.AUTHENTICATION_FAILED]
});
exports.operatorAuthenticationFailed = operatorAuthenticationFailed;
const operatorGameNotFound = () => ({
    code: operator_entities_1.operatorErrorCodes.GAME_NOT_FOUND,
    status: operator_entities_1.operatorStatusMessages[operator_entities_1.operatorErrorCodes.GAME_NOT_FOUND]
});
exports.operatorGameNotFound = operatorGameNotFound;
const operatorBetAlreadySettled = () => ({
    code: operator_entities_1.operatorErrorCodes.BET_ALREADY_SETTLED,
    status: operator_entities_1.operatorStatusMessages[operator_entities_1.operatorErrorCodes.BET_ALREADY_SETTLED]
});
exports.operatorBetAlreadySettled = operatorBetAlreadySettled;
const operatorPlayerSuspended = () => ({
    code: operator_entities_1.operatorErrorCodes.PLAYER_SUSPENDED,
    status: operator_entities_1.operatorStatusMessages[operator_entities_1.operatorErrorCodes.PLAYER_SUSPENDED]
});
exports.operatorPlayerSuspended = operatorPlayerSuspended;
const operatorUnauthorizedRequest = () => ({
    code: operator_entities_1.operatorErrorCodes.UNAUTHORIZED_REQUEST,
    status: operator_entities_1.operatorStatusMessages[operator_entities_1.operatorErrorCodes.UNAUTHORIZED_REQUEST]
});
exports.operatorUnauthorizedRequest = operatorUnauthorizedRequest;
const retryCondition = (err) => err instanceof sw_wallet_adapter_core_1.ConnectionError ||
    err instanceof GeneralError ||
    err instanceof sw_wallet_adapter_core_1.MerchantAdapterAPIError;
exports.retryCondition = retryCondition;
const rollbackCondition = (err) => err instanceof sw_wallet_adapter_core_1.ConnectionError ||
    err instanceof GeneralError ||
    err instanceof OperatorBettingOffError ||
    err instanceof OperatorLimitReachedError ||
    err instanceof OperatorPlayerSuspendedError;
exports.rollbackCondition = rollbackCondition;
const refundCondition = (err) => err instanceof OperatorTokenExpiredError ||
    err instanceof OperatorCustomerNotFoundError ||
    err instanceof AuthenticateFailedError;
exports.refundCondition = refundCondition;
//# sourceMappingURL=operator.errors.js.map