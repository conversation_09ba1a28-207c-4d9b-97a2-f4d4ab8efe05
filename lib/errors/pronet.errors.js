"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PCESInvalidCasinoVendorError = exports.PCESInvalidGameError = exports.PCESAllBetAreOffError = exports.PCESNegativeWithdrawalError = exports.PCESNegativeDepositError = exports.PCESDataOutOfRangeError = exports.PCESRequiredFieldMissingError = exports.PCESInvalidCurrencyError = exports.PCESTransactionNotFoundError = exports.PCESPromotionTypeNotSupportedError = exports.PCESInternalCacheError = exports.PCESUnsupportedApiVersionError = exports.PCESTokenCustomerMismatchError = exports.PCESNotIntegratedError = exports.PCESUnauthorizedRequestError = exports.PCESProfitLimitReachedError = exports.PCESSessionLimitReachedError = exports.PCESLossLimitReachedError = exports.PCESBetLimitReachedError = exports.PCESPlayerSuspendedError = exports.PCESGameNotFoundError = exports.PCESCustomerNotFoundError = exports.PCESTokenNotFoundError = exports.PCESTokenInvalidError = exports.PCESTokenTimeoutError = exports.PCESInsufficientFundsError = exports.PCESBetRecordNotFoundError = exports.PCESBetAlreadyWonError = exports.PCESBetAlreadySettledError = exports.PCESAuthenticationError = exports.PCESError = exports.ValidationError = void 0;
exports.createPCESErrorFromCode = createPCESErrorFromCode;
exports.refundCondition = refundCondition;
exports.rollbackCondition = rollbackCondition;
class ValidationError extends Error {
    constructor(message) {
        super(message);
        this.name = "ValidationError";
    }
}
exports.ValidationError = ValidationError;
class PCESError extends Error {
    constructor(code, status, message) {
        super(message || status);
        this.name = "PCESError";
        this.code = code;
        this.status = status;
    }
}
exports.PCESError = PCESError;
class PCESAuthenticationError extends PCESError {
    constructor(message) {
        super(-20101, "AUTHENTICATION_FAILED", message);
        this.name = "PCESAuthenticationError";
    }
}
exports.PCESAuthenticationError = PCESAuthenticationError;
class PCESBetAlreadySettledError extends PCESError {
    constructor(message) {
        super(-20310, "BET_ALREADY_SETTLED", message);
        this.name = "PCESBetAlreadySettledError";
    }
}
exports.PCESBetAlreadySettledError = PCESBetAlreadySettledError;
class PCESBetAlreadyWonError extends PCESError {
    constructor(message) {
        super(-20112, "BET_ALREADY_WON", message);
        this.name = "PCESBetAlreadyWonError";
    }
}
exports.PCESBetAlreadyWonError = PCESBetAlreadyWonError;
class PCESBetRecordNotFoundError extends PCESError {
    constructor(message) {
        super(-20120, "BET_RECORD_NOT_FOUND", message);
        this.name = "PCESBetRecordNotFoundError";
    }
}
exports.PCESBetRecordNotFoundError = PCESBetRecordNotFoundError;
class PCESInsufficientFundsError extends PCESError {
    constructor(message) {
        super(-20306, "INSUFFICIENT_FUNDS", message);
        this.name = "PCESInsufficientFundsError";
    }
}
exports.PCESInsufficientFundsError = PCESInsufficientFundsError;
class PCESTokenTimeoutError extends PCESError {
    constructor(message) {
        super(-20311, "TOKEN_TIMEOUT", message);
        this.name = "PCESTokenTimeoutError";
    }
}
exports.PCESTokenTimeoutError = PCESTokenTimeoutError;
class PCESTokenInvalidError extends PCESError {
    constructor(message) {
        super(-20312, "TOKEN_INVALID", message);
        this.name = "PCESTokenInvalidError";
    }
}
exports.PCESTokenInvalidError = PCESTokenInvalidError;
class PCESTokenNotFoundError extends PCESError {
    constructor(message) {
        super(-20316, "TOKEN_NOT_FOUND", message);
        this.name = "PCESTokenNotFoundError";
    }
}
exports.PCESTokenNotFoundError = PCESTokenNotFoundError;
class PCESCustomerNotFoundError extends PCESError {
    constructor(message) {
        super(-20304, "CUSTOMER_NOT_FOUND", message);
        this.name = "PCESCustomerNotFoundError";
    }
}
exports.PCESCustomerNotFoundError = PCESCustomerNotFoundError;
class PCESGameNotFoundError extends PCESError {
    constructor(message) {
        super(-20130, "GAME_NOT_FOUND", message);
        this.name = "PCESGameNotFoundError";
    }
}
exports.PCESGameNotFoundError = PCESGameNotFoundError;
class PCESPlayerSuspendedError extends PCESError {
    constructor(message) {
        super(-20307, "PLAYER_SUSPENDED", message);
        this.name = "PCESPlayerSuspendedError";
    }
}
exports.PCESPlayerSuspendedError = PCESPlayerSuspendedError;
class PCESBetLimitReachedError extends PCESError {
    constructor(message) {
        super(-20201, "BET_LIMIT_REACHED", message);
        this.name = "PCESBetLimitReachedError";
    }
}
exports.PCESBetLimitReachedError = PCESBetLimitReachedError;
class PCESLossLimitReachedError extends PCESError {
    constructor(message) {
        super(-20202, "LOSS_LIMIT_REACHED", message);
        this.name = "PCESLossLimitReachedError";
    }
}
exports.PCESLossLimitReachedError = PCESLossLimitReachedError;
class PCESSessionLimitReachedError extends PCESError {
    constructor(message) {
        super(-20203, "SESSION_LIMIT_REACHED", message);
        this.name = "PCESSessionLimitReachedError";
    }
}
exports.PCESSessionLimitReachedError = PCESSessionLimitReachedError;
class PCESProfitLimitReachedError extends PCESError {
    constructor(message) {
        super(-20204, "PROFIT_LIMIT_REACHED", message);
        this.name = "PCESProfitLimitReachedError";
    }
}
exports.PCESProfitLimitReachedError = PCESProfitLimitReachedError;
class PCESUnauthorizedRequestError extends PCESError {
    constructor(message) {
        super(2, "UNAUTHORIZED_REQUEST", message);
        this.name = "PCESUnauthorizedRequestError";
    }
}
exports.PCESUnauthorizedRequestError = PCESUnauthorizedRequestError;
class PCESNotIntegratedError extends PCESError {
    constructor(message) {
        super(3, "NOT_INTEGRATED", message);
        this.name = "PCESNotIntegratedError";
    }
}
exports.PCESNotIntegratedError = PCESNotIntegratedError;
class PCESTokenCustomerMismatchError extends PCESError {
    constructor(message) {
        super(4, "TOKEN_CUSTOMER_MISMATCH", message);
        this.name = "PCESTokenCustomerMismatchError";
    }
}
exports.PCESTokenCustomerMismatchError = PCESTokenCustomerMismatchError;
class PCESUnsupportedApiVersionError extends PCESError {
    constructor(message) {
        super(5, "UNSUPPORTED_API_VERSION", message);
        this.name = "PCESUnsupportedApiVersionError";
    }
}
exports.PCESUnsupportedApiVersionError = PCESUnsupportedApiVersionError;
class PCESInternalCacheError extends PCESError {
    constructor(message) {
        super(6, "INTERNAL_CACHE_ERROR", message);
        this.name = "PCESInternalCacheError";
    }
}
exports.PCESInternalCacheError = PCESInternalCacheError;
class PCESPromotionTypeNotSupportedError extends PCESError {
    constructor(message) {
        super(7, "PROMOTION_TYPE_NOT_SUPPORTED", message);
        this.name = "PCESPromotionTypeNotSupportedError";
    }
}
exports.PCESPromotionTypeNotSupportedError = PCESPromotionTypeNotSupportedError;
class PCESTransactionNotFoundError extends PCESError {
    constructor(message) {
        super(-20313, "TRANSACTION_NOT_FOUND", message);
        this.name = "PCESTransactionNotFoundError";
    }
}
exports.PCESTransactionNotFoundError = PCESTransactionNotFoundError;
class PCESInvalidCurrencyError extends PCESError {
    constructor(message) {
        super(-20305, "INVALID_CURRENCY", message);
        this.name = "PCESInvalidCurrencyError";
    }
}
exports.PCESInvalidCurrencyError = PCESInvalidCurrencyError;
class PCESRequiredFieldMissingError extends PCESError {
    constructor(message) {
        super(-20308, "REQUIRED_FIELD_MISSING", message);
        this.name = "PCESRequiredFieldMissingError";
    }
}
exports.PCESRequiredFieldMissingError = PCESRequiredFieldMissingError;
class PCESDataOutOfRangeError extends PCESError {
    constructor(message) {
        super(-20309, "DATA_OUT_OF_RANGE", message);
        this.name = "PCESDataOutOfRangeError";
    }
}
exports.PCESDataOutOfRangeError = PCESDataOutOfRangeError;
class PCESNegativeDepositError extends PCESError {
    constructor(message) {
        super(-20314, "NEGATIVE_DEPOSIT", message);
        this.name = "PCESNegativeDepositError";
    }
}
exports.PCESNegativeDepositError = PCESNegativeDepositError;
class PCESNegativeWithdrawalError extends PCESError {
    constructor(message) {
        super(-20315, "NEGATIVE_WITHDRAWAL", message);
        this.name = "PCESNegativeWithdrawalError";
    }
}
exports.PCESNegativeWithdrawalError = PCESNegativeWithdrawalError;
class PCESAllBetAreOffError extends PCESError {
    constructor(message) {
        super(-20302, "ALL_BET_ARE_OFF", message);
        this.name = "PCESAllBetAreOffError";
    }
}
exports.PCESAllBetAreOffError = PCESAllBetAreOffError;
class PCESInvalidGameError extends PCESError {
    constructor(message) {
        super(-20303, "INVALID_GAME", message);
        this.name = "PCESInvalidGameError";
    }
}
exports.PCESInvalidGameError = PCESInvalidGameError;
class PCESInvalidCasinoVendorError extends PCESError {
    constructor(message) {
        super(-20301, "INVALID_CASINO_VENDOR", message);
        this.name = "PCESInvalidCasinoVendorError";
    }
}
exports.PCESInvalidCasinoVendorError = PCESInvalidCasinoVendorError;
function createPCESErrorFromCode(code, status, message) {
    switch (code) {
        case -20101:
            return new PCESAuthenticationError(message);
        case -20310:
            return new PCESBetAlreadySettledError(message);
        case -20112:
            return new PCESBetAlreadyWonError(message);
        case -20120:
            return new PCESBetRecordNotFoundError(message);
        case -20306:
            return new PCESInsufficientFundsError(message);
        case -20311:
            return new PCESTokenTimeoutError(message);
        case -20312:
            return new PCESTokenInvalidError(message);
        case -20316:
            return new PCESTokenNotFoundError(message);
        case -20304:
            return new PCESCustomerNotFoundError(message);
        case -20130:
            return new PCESGameNotFoundError(message);
        case -20307:
            return new PCESPlayerSuspendedError(message);
        case -20201:
            return new PCESBetLimitReachedError(message);
        case -20202:
            return new PCESLossLimitReachedError(message);
        case -20203:
            return new PCESSessionLimitReachedError(message);
        case -20204:
            return new PCESProfitLimitReachedError(message);
        case 2:
            return new PCESUnauthorizedRequestError(message);
        case 3:
            return new PCESNotIntegratedError(message);
        case 4:
            return new PCESTokenCustomerMismatchError(message);
        case 5:
            return new PCESUnsupportedApiVersionError(message);
        case 6:
            return new PCESInternalCacheError(message);
        case 7:
            return new PCESPromotionTypeNotSupportedError(message);
        case -20313:
            return new PCESTransactionNotFoundError(message);
        case -20305:
            return new PCESInvalidCurrencyError(message);
        case -20308:
            return new PCESRequiredFieldMissingError(message);
        case -20309:
            return new PCESDataOutOfRangeError(message);
        case -20314:
            return new PCESNegativeDepositError(message);
        case -20315:
            return new PCESNegativeWithdrawalError(message);
        case -20302:
            return new PCESAllBetAreOffError(message);
        case -20303:
            return new PCESInvalidGameError(message);
        case -20301:
            return new PCESInvalidCasinoVendorError(message);
        default:
            return new PCESError(code, status, message);
    }
}
function refundCondition(err) {
    return err instanceof PCESInsufficientFundsError ||
        err instanceof PCESBetLimitReachedError ||
        err instanceof PCESLossLimitReachedError ||
        err instanceof PCESSessionLimitReachedError ||
        err instanceof PCESProfitLimitReachedError ||
        err instanceof PCESPlayerSuspendedError ||
        err instanceof PCESAllBetAreOffError;
}
function rollbackCondition(err) {
    return err instanceof PCESTokenTimeoutError ||
        err instanceof PCESTokenInvalidError ||
        err instanceof PCESTokenNotFoundError ||
        err instanceof PCESCustomerNotFoundError ||
        err instanceof PCESGameNotFoundError ||
        err instanceof PCESInvalidGameError ||
        err instanceof PCESInvalidCasinoVendorError ||
        err instanceof PCESAuthenticationError ||
        err instanceof PCESInternalCacheError;
}
//# sourceMappingURL=pronet.errors.js.map