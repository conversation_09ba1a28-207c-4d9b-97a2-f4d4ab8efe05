"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RollbackHttpHandler = void 0;
const baseHttp_handler_1 = require("../../utils/baseHttp.handler");
const common_1 = require("@nestjs/common");
let RollbackHttpHandler = class RollbackHttpHandler extends baseHttp_handler_1.BaseHttpHandler {
    async build(req) {
        const gameTokenData = this.getGameTokenData(req);
        this.validateRequiredFields(gameTokenData, ["customer", "token", "gameId"]);
        this.validateRequiredFields(req.request, ["transactionId"]);
        const customer = this.extractCustomerFromToken(gameTokenData);
        const token = this.extractTokenFromGameData(gameTokenData);
        const gameId = this.extractGameId(gameTokenData);
        const originalTrxId = req.request.transactionId.publicId;
        const rollbackRequest = this.buildOperatorRequest(customer, token, {
            gameId,
            trxId: originalTrxId
        });
        return this.buildHttpRequest({
            endpoint: "rollback",
            method: "post",
            payload: rollbackRequest,
            merchantInfo: req.merchantInfo,
            retryAvailable: true
        });
    }
    async parse(response) {
        const operatorResponse = this.parseHttpResponse(response);
        return {
            main: this.sanitizeAmount(operatorResponse.balance + (operatorResponse.bonusBalance || 0))
        };
    }
};
exports.RollbackHttpHandler = RollbackHttpHandler;
exports.RollbackHttpHandler = RollbackHttpHandler = __decorate([
    (0, common_1.Injectable)()
], RollbackHttpHandler);
//# sourceMappingURL=rollback.http.handler.js.map