"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
const mocha_typescript_1 = require("mocha-typescript");
const chai_1 = require("chai");
const testing_1 = require("@nestjs/testing");
const payment_service_1 = require("./payment.service");
const sw_integration_core_1 = require("@skywind-group/sw-integration-core");
const bet_http_handler_1 = require("./bet.http.handler");
const win_http_handler_1 = require("./win.http.handler");
const balance_http_handler_1 = require("./balance.http.handler");
const debitCredit_http_handler_1 = require("./debitCredit.http.handler");
const rollback_http_handler_1 = require("./rollback.http.handler");
const promo_http_handler_1 = require("./promo.http.handler");
let PaymentServiceSpec = class PaymentServiceSpec {
    async before() {
        const mockHttpGateway = {
            request: async (req, handler) => {
                return createMockBalance();
            }
        };
        const module = await testing_1.Test.createTestingModule({
            providers: [
                payment_service_1.PaymentService,
                { provide: sw_integration_core_1.HttpGateway, useValue: mockHttpGateway },
                { provide: bet_http_handler_1.BetHttpHandler, useValue: {} },
                { provide: win_http_handler_1.WinHttpHandler, useValue: {} },
                { provide: balance_http_handler_1.BalanceHttpHandler, useValue: {} },
                { provide: debitCredit_http_handler_1.DebitCreditHttpHandler, useValue: {} },
                { provide: rollback_http_handler_1.RollbackHttpHandler, useValue: {} },
                { provide: promo_http_handler_1.PromoHttpHandler, useValue: {} }
            ]
        }).compile();
        this.paymentService = module.get(payment_service_1.PaymentService);
        this.httpGateway = module.get(sw_integration_core_1.HttpGateway);
    }
    async "should get balance successfully"() {
        const request = createMockPaymentRequest();
        const expectedBalance = createMockBalance();
        const result = await this.paymentService.getBalance(request);
        (0, chai_1.expect)(result).to.deep.equal(expectedBalance);
    }
    async "should process bet payment successfully"() {
        const request = createMockPaymentRequest();
        request.request.bet = 10.00;
        const expectedBalance = createMockBalance();
        const result = await this.paymentService.commitBetPayment(request);
        (0, chai_1.expect)(result).to.deep.equal(expectedBalance);
    }
    async "should process win payment successfully"() {
        const request = createMockPaymentRequest();
        request.request.totalWin = 25.00;
        const expectedBalance = createMockBalance();
        const result = await this.paymentService.commitWinPayment(request);
        (0, chai_1.expect)(result).to.deep.equal(expectedBalance);
    }
    async "should process jackpot win payment successfully"() {
        const request = createMockPaymentRequest();
        request.request.totalWin = 1000.00;
        const expectedBalance = createMockBalance();
        const result = await this.paymentService.commitJackpotWinPayment(request);
        (0, chai_1.expect)(result).to.deep.equal(expectedBalance);
    }
    async "should process bonus payment successfully"() {
        const request = createMockPaymentRequest();
        request.request.totalWin = 50.00;
        request.request.promoType = "freespin";
        const expectedBalance = createMockBalance();
        const result = await this.paymentService.commitBonusPayment(request);
        (0, chai_1.expect)(result).to.deep.equal(expectedBalance);
    }
    async "should return balance for zero bet"() {
        const request = createMockPaymentRequest();
        request.request.bet = 0;
        const expectedBalance = createMockBalance();
        const result = await this.paymentService.commitBetPayment(request);
        (0, chai_1.expect)(result).to.deep.equal(expectedBalance);
    }
    async "should return balance for zero win"() {
        const request = createMockPaymentRequest();
        request.request.totalWin = 0;
        const expectedBalance = createMockBalance();
        const result = await this.paymentService.commitWinPayment(request);
        (0, chai_1.expect)(result).to.deep.equal(expectedBalance);
    }
};
__decorate([
    mocha_typescript_1.test,
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PaymentServiceSpec.prototype, "should get balance successfully", null);
__decorate([
    mocha_typescript_1.test,
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PaymentServiceSpec.prototype, "should process bet payment successfully", null);
__decorate([
    mocha_typescript_1.test,
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PaymentServiceSpec.prototype, "should process win payment successfully", null);
__decorate([
    mocha_typescript_1.test,
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PaymentServiceSpec.prototype, "should process jackpot win payment successfully", null);
__decorate([
    mocha_typescript_1.test,
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PaymentServiceSpec.prototype, "should process bonus payment successfully", null);
__decorate([
    mocha_typescript_1.test,
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PaymentServiceSpec.prototype, "should return balance for zero bet", null);
__decorate([
    mocha_typescript_1.test,
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PaymentServiceSpec.prototype, "should return balance for zero win", null);
PaymentServiceSpec = __decorate([
    mocha_typescript_1.suite
], PaymentServiceSpec);
function createMockPaymentRequest() {
    return {
        gameTokenData: {
            customer: "test_customer",
            token: "test_token",
            gameId: "test_game",
            currency: "USD",
            demo: false,
            platform: "d",
            language: "en",
            country: "US",
            trader: "test_trader",
            playerCode: "test_customer",
            gameCode: "test_game",
            isPromoInternal: false,
            brandId: 1,
            merchantType: "pronet",
            merchantCode: "test_merchant"
        },
        request: {
            bet: 10.00,
            totalWin: 0,
            transactionId: {
                publicId: "test_txn_123",
                serialId: 123,
                timestamp: Date.now()
            },
            roundPID: "test_round_123",
            gameToken: "test_token",
            ts: new Date().toISOString(),
            roundId: "test_round_123"
        },
        merchantInfo: {
            code: "test_merchant",
            type: "pronet",
            params: {},
            brandId: 1
        }
    };
}
function createMockBalance() {
    return {
        main: 100.50
    };
}
//# sourceMappingURL=payment.service.spec.js.map