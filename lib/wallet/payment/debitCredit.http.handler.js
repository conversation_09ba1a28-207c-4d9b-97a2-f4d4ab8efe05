"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DebitCreditHttpHandler = void 0;
const baseHttp_handler_1 = require("../../utils/baseHttp.handler");
const operator_utils_1 = require("../../utils/operator.utils");
const common_1 = require("@nestjs/common");
let DebitCreditHttpHandler = class DebitCreditHttpHandler extends baseHttp_handler_1.BaseHttpHandler {
    async build(req) {
        const gameTokenData = this.getGameTokenData(req);
        this.validateRequiredFields(gameTokenData, ["customer", "token", "gameId", "currency"]);
        this.validateRequiredFields(req.request, ["bet", "totalWin", "transactionId", "roundPID"]);
        const customer = this.extractCustomerFromToken(gameTokenData);
        const token = this.extractTokenFromGameData(gameTokenData);
        const gameId = this.extractGameId(gameTokenData);
        const currency = this.extractCurrency(gameTokenData);
        const betAmount = this.sanitizeAmount((0, operator_utils_1.sumMajorUnits)(req.request.bet));
        const winAmount = this.sanitizeAmount((0, operator_utils_1.sumMajorUnits)(req.request.totalWin));
        const betId = this.generateBetId(req.request.roundPID);
        const trxId = this.generateTransactionId("bet");
        const creditTrxId = this.generateTransactionId("win");
        const debitCreditRequest = this.buildOperatorRequest(customer, token, {
            gameId,
            amount: betAmount,
            creditAmount: winAmount,
            currency,
            betId,
            trxId,
            creditTrxId,
            tip: false
        });
        return this.buildHttpRequest({
            endpoint: "debit-credit",
            method: "post",
            payload: debitCreditRequest,
            merchantInfo: req.merchantInfo,
            retryAvailable: true
        });
    }
    async parse(response) {
        const operatorResponse = this.parseHttpResponse(response);
        return {
            main: this.sanitizeAmount(operatorResponse.balance + (operatorResponse.bonusBalance || 0))
        };
    }
};
exports.DebitCreditHttpHandler = DebitCreditHttpHandler;
exports.DebitCreditHttpHandler = DebitCreditHttpHandler = __decorate([
    (0, common_1.Injectable)()
], DebitCreditHttpHandler);
//# sourceMappingURL=debitCredit.http.handler.js.map