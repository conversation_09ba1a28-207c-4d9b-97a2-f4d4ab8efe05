"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PromoHttpHandler = void 0;
const baseHttp_handler_1 = require("../../utils/baseHttp.handler");
const operator_utils_1 = require("../../utils/operator.utils");
const common_1 = require("@nestjs/common");
let PromoHttpHandler = class PromoHttpHandler extends baseHttp_handler_1.BaseHttpHandler {
    async build(req) {
        const gameTokenData = this.getGameTokenData(req);
        this.validateRequiredFields(gameTokenData, ["customer", "token", "gameId", "currency"]);
        this.validateRequiredFields(req.request, ["totalWin", "transactionId", "roundPID"]);
        this.validateRequiredFields(req.request, ["promoType"]);
        const customer = this.extractCustomerFromToken(gameTokenData);
        const token = this.extractTokenFromGameData(gameTokenData);
        const gameId = this.extractGameId(gameTokenData);
        const currency = this.extractCurrency(gameTokenData);
        const promoAmount = this.sanitizeAmount((0, operator_utils_1.sumMajorUnits)(req.request.totalWin));
        const betId = this.generateBetId(req.request.roundPID);
        const trxId = this.generateTransactionId("promo");
        const promoRequest = this.buildOperatorRequest(customer, token, {
            gameId,
            amount: promoAmount,
            currency,
            betId,
            trxId,
            promo: this.buildPromoInfo(req.request.promoType, req.request.externalId, this.extractFreespinData(req.request))
        });
        return this.buildHttpRequest({
            endpoint: "promo",
            method: "post",
            payload: promoRequest,
            merchantInfo: req.merchantInfo,
            retryAvailable: true
        });
    }
    extractFreespinData(paymentRequest) {
        if (!paymentRequest.freeBetMode) {
            return undefined;
        }
        return {
            freespinRef: paymentRequest.externalId || paymentRequest.promoId,
            requested: paymentRequest.freeBetMode || false,
            remainingRounds: paymentRequest.freeBetBalance?.amount,
            totalWinnings: undefined
        };
    }
    async parse(response) {
        const operatorResponse = this.parseHttpResponse(response);
        return {
            main: this.sanitizeAmount(operatorResponse.balance + (operatorResponse.bonusBalance || 0))
        };
    }
};
exports.PromoHttpHandler = PromoHttpHandler;
exports.PromoHttpHandler = PromoHttpHandler = __decorate([
    (0, common_1.Injectable)()
], PromoHttpHandler);
//# sourceMappingURL=promo.http.handler.js.map