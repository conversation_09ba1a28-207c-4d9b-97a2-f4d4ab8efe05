"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GameUrlService = void 0;
const common_1 = require("@nestjs/common");
const sw_utils_1 = require("@skywind-group/sw-utils");
const operator_utils_1 = require("../../utils/operator.utils");
const _config_1 = require("../../config");
const log = sw_utils_1.logging.logger("GameUrlService");
const { measure } = sw_utils_1.measures;
let GameUrlService = class GameUrlService {
    async createGameUrl(req) {
        const baseUrl = _config_1.default.http.operatorUrl;
        const request = req.initRequest;
        let gameParams;
        if (request.demo) {
            gameParams = (0, operator_utils_1.createDemoGameParams)(request.gameId, request.lang, request.platform, request.trader, request.lobby, request.tableId);
        }
        else {
            gameParams = (0, operator_utils_1.createRealGameParams)(request.currency, request.customer, request.gameId, request.lang, request.platform, request.token, request.trader, request.country, request.lobby, request.tableId);
        }
        const gameUrl = (0, operator_utils_1.buildGameUrl)(`${baseUrl}/casino-engine/game`, gameParams);
        log.info("Created game URL for operator", {
            customer: request.customer,
            gameId: request.gameId,
            demo: request.demo,
            url: gameUrl
        });
        return {
            urlParams: gameParams,
            tokenData: {
                playerCode: request.customer || "",
                currency: request.currency || "",
                gameCode: req.gameCode,
                providerCode: req.providerCode,
                providerGameCode: req.providerGameCode,
                language: request.lang,
                country: request.country,
                brandId: 1,
                merchantType: "pronet",
                merchantCode: "pronet"
            }
        };
    }
};
exports.GameUrlService = GameUrlService;
__decorate([
    measure({ name: "GameUrlService.createGameUrl", isAsync: true }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], GameUrlService.prototype, "createGameUrl", null);
exports.GameUrlService = GameUrlService = __decorate([
    (0, common_1.Injectable)()
], GameUrlService);
//# sourceMappingURL=gameUrl.service.js.map