"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var LauncherService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.LauncherService = void 0;
const common_1 = require("@nestjs/common");
const sw_wallet_adapter_core_1 = require("@skywind-group/sw-wallet-adapter-core");
const operator_errors_1 = require("../errors/operator.errors");
const _names_1 = require("../names");
const sw_utils_1 = require("@skywind-group/sw-utils");
const jwt = require("jsonwebtoken");
const operator_utils_1 = require("../utils/operator.utils");
const _config_1 = require("../config");
const log = sw_utils_1.logging.logger("LauncherService");
const { measure } = sw_utils_1.measures;
let LauncherService = LauncherService_1 = class LauncherService {
    constructor(baseHttpService) {
        this.baseHttpService = baseHttpService;
    }
    async getGameUrl(data, ip) {
        this.validateRequest(data);
        const sanitizedData = this.sanitizeData(data);
        const options = this.mapToSW(sanitizedData, ip);
        const { token, url } = await this.baseHttpService.post(LauncherService_1.FALCON_GAME_URL, options);
        if (token) {
            const startGameToken = this.parseToken(token);
            if (startGameToken.loginFailed) {
                return startGameToken.relaunchUrl;
            }
        }
        return url;
    }
    buildOperatorGameUrl(data) {
        this.validateRequest(data);
        const sanitizedData = this.sanitizeData(data);
        const baseUrl = _config_1.default.http.operatorUrl;
        let gameParams;
        if (sanitizedData.demo) {
            gameParams = (0, operator_utils_1.createDemoGameParams)(sanitizedData.gameId, sanitizedData.lang, sanitizedData.platform, sanitizedData.trader, sanitizedData.lobby, sanitizedData.tableId);
        }
        else {
            if (!sanitizedData.customer || !sanitizedData.token) {
                throw new operator_errors_1.ValidationError("Customer and token are required for real money games");
            }
            gameParams = (0, operator_utils_1.createRealGameParams)(sanitizedData.currency, sanitizedData.customer, sanitizedData.gameId, sanitizedData.lang, sanitizedData.platform, sanitizedData.token, sanitizedData.trader, sanitizedData.country, sanitizedData.lobby, sanitizedData.tableId);
        }
        return (0, operator_utils_1.buildGameUrl)(`${baseUrl}/casino-engine/game`, gameParams);
    }
    validateRequest(data) {
        if (!data.gameId || data.gameId.trim() === "") {
            throw new operator_errors_1.ValidationError("Game ID is required");
        }
        if (!data.lang || data.lang.trim() === "") {
            throw new operator_errors_1.ValidationError("Language is required");
        }
        if (!data.trader || data.trader.trim() === "") {
            throw new operator_errors_1.ValidationError("Trader is required");
        }
        if (!data.demo && (!data.customer || !data.token)) {
            throw new operator_errors_1.ValidationError("Customer and token are required for real money games");
        }
        if (!data.demo && (!data.currency || data.currency.trim() === "")) {
            throw new operator_errors_1.ValidationError("Currency is required for real money games");
        }
        if (!["d", "m", "desktop", "mobile"].includes(data.platform)) {
            throw new operator_errors_1.ValidationError("Invalid platform. Must be 'd', 'm', 'desktop', or 'mobile'");
        }
    }
    sanitizeData(data) {
        return {
            ...data,
            gameId: (0, operator_utils_1.sanitizeGameId)(data.gameId),
            lang: data.lang.toLowerCase(),
            currency: data.currency ? data.currency.toUpperCase() : "FUN",
            platform: (0, operator_utils_1.parsePlatformType)(data.platform),
            tableId: (0, operator_utils_1.sanitizeTableId)(data.tableId),
            trader: data.trader.trim(),
            lobby: data.lobby?.trim(),
            country: data.country?.toUpperCase(),
            customer: data.customer?.trim(),
            token: data.token?.trim()
        };
    }
    mapToSW(data, ip) {
        const playMode = data.demo ? sw_wallet_adapter_core_1.PlayMode.FUN : sw_wallet_adapter_core_1.PlayMode.REAL;
        return {
            gameCode: data.gameId,
            playMode: playMode,
            publicToken: data.token,
            language: data.lang,
            currency: data.currency,
            platform: data.platform === "d" ? "desktop" : "mobile",
            playerIp: ip,
            merchantCode: _config_1.default.merchantCode,
            merchantType: _config_1.default.merchantType,
            trader: data.trader,
            tableId: data.tableId,
            lobby: data.lobby,
            country: data.country,
            demo: data.demo,
            customer: data.customer
        };
    }
    parseToken(token) {
        try {
            return jwt.decode(token);
        }
        catch (err) {
            log.error(err, "Failed to parse game token");
            throw new operator_errors_1.ValidationError("Invalid game token");
        }
    }
};
exports.LauncherService = LauncherService;
LauncherService.FALCON_GAME_URL = "v1/merchants/game/url";
__decorate([
    measure({ name: "LauncherService.getGameUrl", isAsync: true }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], LauncherService.prototype, "getGameUrl", null);
__decorate([
    measure({ name: "LauncherService.buildOperatorGameUrl", isAsync: false }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", String)
], LauncherService.prototype, "buildOperatorGameUrl", null);
exports.LauncherService = LauncherService = LauncherService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(_names_1.Names.BaseHttpService)),
    __metadata("design:paramtypes", [sw_wallet_adapter_core_1.BaseHttpService])
], LauncherService);
//# sourceMappingURL=launcher.service.js.map