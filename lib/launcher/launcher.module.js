"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LauncherModule = void 0;
const common_1 = require("@nestjs/common");
const launcher_controller_1 = require("./launcher.controller");
const launcher_service_1 = require("./launcher.service");
const sw_wallet_adapter_core_1 = require("@skywind-group/sw-wallet-adapter-core");
const _config_1 = require("../config");
const _names_1 = require("../names");
let LauncherModule = class LauncherModule {
};
exports.LauncherModule = LauncherModule;
exports.LauncherModule = LauncherModule = __decorate([
    (0, common_1.Module)({
        controllers: [launcher_controller_1.LauncherController],
        providers: [
            launcher_service_1.LauncherService,
            {
                provide: _names_1.Names.BaseHttpService,
                useFactory: () => {
                    return new sw_wallet_adapter_core_1.BaseHttpService(_config_1.default.http.operatorUrl);
                }
            }
        ]
    })
], LauncherModule);
//# sourceMappingURL=launcher.module.js.map