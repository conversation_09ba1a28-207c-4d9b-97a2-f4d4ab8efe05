"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OperatorModule = void 0;
const common_1 = require("@nestjs/common");
const history_controller_1 = require("../controllers/history.controller");
const history_service_1 = require("../services/history.service");
const sw_wallet_adapter_core_1 = require("@skywind-group/sw-wallet-adapter-core");
const sw_integration_core_1 = require("@skywind-group/sw-integration-core");
const _config_1 = require("../../config");
const _names_1 = require("../../names");
let OperatorModule = class OperatorModule {
};
exports.OperatorModule = OperatorModule;
exports.OperatorModule = OperatorModule = __decorate([
    (0, common_1.Module)({
        controllers: [history_controller_1.HistoryController],
        providers: [
            history_service_1.HistoryService,
            {
                provide: _names_1.Names.InternalAPIService,
                useFactory: () => {
                    return new sw_wallet_adapter_core_1.InternalAPIService(_config_1.default.operator.roundDetailsUrl);
                }
            },
            {
                provide: sw_integration_core_1.HttpGateway,
                useFactory: () => {
                    return new sw_integration_core_1.HttpGateway(_config_1.default.http);
                }
            }
        ]
    })
], OperatorModule);
//# sourceMappingURL=operator.module.js.map