"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.successResponses = exports.BaseHttpHandler = void 0;
const _config_1 = require("../config");
const operator_errors_1 = require("../errors/operator.errors");
const operator_utils_1 = require("./operator.utils");
class BaseHttpHandler {
    buildBaseRequest(customer, token) {
        return {
            customer,
            token
        };
    }
    buildHttpRequest(request) {
        const url = (0, operator_utils_1.buildOperatorApiUrl)(request.endpoint);
        const hash = request.payload ? (0, operator_utils_1.calculateOperatorHash)(request.payload) : "";
        if (request.payload) {
            request.payload.hash = hash;
        }
        return {
            url,
            method: request.method,
            headers: (0, operator_utils_1.buildOperatorHeaders)(hash),
            payload: request.payload,
            timeout: _config_1.default.http.defaultOptions.timeout,
            proxy: _config_1.default.http.defaultOptions.proxy,
            ssl: _config_1.default.http.ssl,
            retryAvailable: request.retryAvailable !== false
        };
    }
    parseHttpResponse(response) {
        const responseBody = response.body;
        if (!(0, operator_utils_1.isOperatorSuccess)(responseBody)) {
            throw (0, operator_errors_1.mapOperatorToSWError)({
                code: responseBody.code || -1,
                status: responseBody.status || "Unknown error"
            });
        }
        return responseBody;
    }
    getGameTokenData(req) {
        return req.gameTokenData || req.request?.gameTokenData;
    }
    buildOperatorRequest(customer, token, additionalFields = {}) {
        return {
            ...this.buildBaseRequest(customer, token),
            ...additionalFields
        };
    }
    validateRequiredFields(obj, fields) {
        const missingFields = fields.filter(field => !obj[field]);
        if (missingFields.length > 0) {
            throw new Error(`Missing required fields: ${missingFields.join(", ")}`);
        }
    }
    sanitizeAmount(amount) {
        return Math.round(amount * 100) / 100;
    }
    formatCurrency(currency, demo) {
        return demo ? "FUN" : currency.toUpperCase();
    }
    generateTransactionId(prefix = "trx") {
        return `${prefix}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }
    generateBetId(roundId) {
        return `bet_${roundId}_${Date.now()}`;
    }
    extractCustomerFromToken(gameTokenData) {
        return gameTokenData.customer || gameTokenData.playerCode;
    }
    extractTokenFromGameData(gameTokenData) {
        return gameTokenData.token || gameTokenData.privateToken;
    }
    extractGameId(gameTokenData) {
        return gameTokenData.gameId || gameTokenData.gameCode;
    }
    extractCurrency(gameTokenData) {
        return this.formatCurrency(gameTokenData.currency, gameTokenData.demo || false);
    }
    buildFreespinInfo(freeSpinData) {
        if (!freeSpinData) {
            return undefined;
        }
        return {
            freespinRef: freeSpinData.freespinRef,
            requested: freeSpinData.requested || false,
            remainingRounds: freeSpinData.remainingRounds,
            totalWinnings: freeSpinData.totalWinnings
        };
    }
    buildPromoInfo(promoType, promoRef, freeSpinData) {
        if (!promoType || !promoRef) {
            return undefined;
        }
        return {
            promoType,
            promoRef,
            freeSpinData: this.buildFreespinInfo(freeSpinData)
        };
    }
}
exports.BaseHttpHandler = BaseHttpHandler;
exports.successResponses = [200, 201, 202];
//# sourceMappingURL=baseHttp.handler.js.map