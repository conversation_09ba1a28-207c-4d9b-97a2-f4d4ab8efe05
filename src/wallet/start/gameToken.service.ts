import { IntegrationGameTokenData, IntegrationStartGameTokenData } from "@entities/operator.entities";
import { ValidationError } from "@errors/operator.errors";
import { Injectable } from "@nestjs/common";
import { CreateGameTokenRequest, CreateGameTokenSupport } from "@skywind-group/sw-integration-core";
import { logging, measures } from "@skywind-group/sw-utils";
import { MerchantGameTokenInfo } from "@skywind-group/sw-wallet-adapter-core";

const log = logging.logger("GameTokenService");
const { measure } = measures;

@Injectable()
export class GameTokenService implements CreateGameTokenSupport<IntegrationStartGameTokenData, IntegrationGameTokenData> {
    
    @measure({ name: "GameTokenService.createGameTokenData", isAsync: true })
    public async createGameTokenData(req: CreateGameTokenRequest<IntegrationStartGameTokenData>): Promise<MerchantGameTokenInfo<IntegrationGameTokenData>> {
        this.validateRequest(req);

        const gameTokenData: IntegrationGameTokenData = {
            // SW standard fields
            playerCode: req.startGameToken.playerCode,
            currency: req.currency,
            gameCode: req.gameCode,
            isPromoInternal: false,
            brandId: req.startGameToken.brandId,
            merchantType: req.startGameToken.merchantType,
            merchantCode: req.startGameToken.merchantCode,

            // operator-specific fields
            customer: req.startGameToken.customer,
            token: req.startGameToken.token,
            gameId: req.startGameToken.gameId,
            demo: req.startGameToken.demo,
            platform: req.startGameToken.platform,
            language: req.startGameToken.language,
            country: req.startGameToken.country,
            trader: req.startGameToken.trader,
            tableId: req.startGameToken.tableId,
            lobby: req.startGameToken.lobby
        };

        log.info("Created game token for operator", {
            customer: gameTokenData.customer,
            gameId: gameTokenData.gameId,
            demo: gameTokenData.demo
        });

        return {
            gameTokenData
        };
    }
    
    private validateRequest(req: CreateGameTokenRequest<IntegrationStartGameTokenData>): void {
        if (!req.gameCode || req.gameCode.trim() === "") {
            throw new ValidationError("Game code is required");
        }

        if (!req.startGameToken.gameId || req.startGameToken.gameId.trim() === "") {
            throw new ValidationError("Game ID is required");
        }

        if (!req.startGameToken.trader || req.startGameToken.trader.trim() === "") {
            throw new ValidationError("Trader is required");
        }

        if (!req.startGameToken.demo && (!req.startGameToken.customer || !req.startGameToken.token)) {
            throw new ValidationError("Customer and token are required for real money games");
        }

        if (!req.startGameToken.demo && (!req.currency || req.currency.trim() === "")) {
            throw new ValidationError("Currency is required for real money games");
        }
    }
}
