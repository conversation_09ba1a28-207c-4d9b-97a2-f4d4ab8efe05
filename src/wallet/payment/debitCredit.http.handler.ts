import { CommitPaymentRequest, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { IntegrationGameTokenData, IntegrationPaymentRequest } from "@entities/operator.entities";
import { <PERSON><PERSON>ttp<PERSON><PERSON><PERSON>, HttpHandlerRequest } from "@utils/baseHttp.handler";
import * as superagent from "superagent";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import {
    OperatorDebitCreditRequest,
    OperatorDebitCreditResponse
} from "@entities/operator.entities";
import { sumMajorUnits } from "@utils/operator.utils";
import { Injectable } from "@nestjs/common";

@Injectable()
export class DebitCreditHttpHandler extends BaseHttpHandler
    implements HttpHandler<CommitPaymentRequest<IntegrationGameTokenData>, Balance> {
    
    public async build(req: IntegrationPaymentRequest): Promise<HTTPOperatorRequest> {
        const gameTokenData = this.getGameTokenData(req);
        
        this.validateRequiredFields(gameTokenData, ["customer", "token", "gameId", "currency"]);
        this.validateRequiredFields(req.request, ["bet", "totalWin", "transactionId", "roundPID"]);

        const customer = this.extractCustomerFromToken(gameTokenData);
        const token = this.extractTokenFromGameData(gameTokenData);
        const gameId = this.extractGameId(gameTokenData);
        const currency = this.extractCurrency(gameTokenData);
        
        const betAmount = this.sanitizeAmount(sumMajorUnits(req.request.bet));
        const winAmount = this.sanitizeAmount(sumMajorUnits(req.request.totalWin));
        const betId = this.generateBetId(req.request.roundPID);
        const trxId = this.generateTransactionId("bet");
        const creditTrxId = this.generateTransactionId("win");

        const debitCreditRequest: OperatorDebitCreditRequest = this.buildOperatorRequest(customer, token, {
            gameId,
            amount: betAmount,
            creditAmount: winAmount,
            currency,
            betId,
            trxId,
            creditTrxId,
            tip: false
        });

        return this.buildHttpRequest({
            endpoint: "debit-credit",
            method: "post",
            payload: debitCreditRequest,
            merchantInfo: req.merchantInfo,
            retryAvailable: true
        } as HttpHandlerRequest);
    }

    public async parse(response: superagent.Response): Promise<Balance> {
        const operatorResponse = this.parseHttpResponse<OperatorDebitCreditResponse>(response);
        
        return {
            main: this.sanitizeAmount(operatorResponse.balance + (operatorResponse.bonusBalance || 0))
        };
    }
}
