import { CommitPaymentRequest, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { IntegrationGameTokenData } from "@entities/operator.entities";
import { BaseHttpHand<PERSON>, HttpHandlerRequest } from "@utils/baseHttp.handler";
import * as superagent from "superagent";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import {
    OperatorCreditRequest,
    OperatorCreditResponse
} from "@entities/operator.entities";
import { sumMajorUnits } from "@utils/operator.utils";
import { Injectable } from "@nestjs/common";

@Injectable()
export class WinHttpHandler extends BaseHttpHandler
    implements HttpHandler<CommitPaymentRequest<IntegrationGameTokenData>, Balance> {
    
    public async build(req: CommitPaymentRequest<IntegrationGameTokenData>): Promise<HTTPOperatorRequest> {
        const gameTokenData = this.getGameTokenData(req);
        
        this.validateRequiredFields(gameTokenData, ["customer", "token", "gameId", "currency"]);
        this.validateRequiredFields(req.request, ["totalWin", "transactionId", "roundPID"]);

        const customer = this.extractCustomerFromToken(gameTokenData);
        const token = this.extractTokenFromGameData(gameTokenData);
        const gameId = this.extractGameId(gameTokenData);
        const currency = this.extractCurrency(gameTokenData);
        
        const winAmount = this.sanitizeAmount(sumMajorUnits(req.request.totalWin));
        const betId = this.generateBetId(req.request.roundPID);
        const trxId = this.generateTransactionId("win");

        const creditRequest: OperatorCreditRequest = this.buildOperatorRequest(customer, token, {
            gameId,
            amount: winAmount,
            currency,
            betId,
            trxId,
            freespin: this.buildFreespinInfo(this.extractFreespinData(req.request))
        });

        return this.buildHttpRequest({
            endpoint: "credit",
            method: "post",
            payload: creditRequest,
            merchantInfo: req.merchantInfo,
            retryAvailable: true
        } as HttpHandlerRequest);
    }

    private extractFreespinData(paymentRequest: any): any {
        if (!paymentRequest.freeBetMode) {
            return undefined;
        }

        return {
            freespinRef: paymentRequest.externalId || paymentRequest.promoId,
            requested: paymentRequest.freeBetMode || false,
            remainingRounds: paymentRequest.freeBetBalance?.amount,
            totalWinnings: undefined
        };
    }

    public async parse(response: superagent.Response): Promise<Balance> {
        const operatorResponse = this.parseHttpResponse<OperatorCreditResponse>(response);
        
        return {
            main: this.sanitizeAmount(operatorResponse.balance + (operatorResponse.bonusBalance || 0))
        };
    }
}
