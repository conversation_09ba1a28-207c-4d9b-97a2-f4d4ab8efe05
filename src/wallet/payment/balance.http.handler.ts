import { IntegrationPaymentRequest, OperatorAuthRequest, OperatorAuthResponse } from "@entities/operator.entities";
import { Injectable } from "@nestjs/common";
import { <PERSON>ttp<PERSON><PERSON><PERSON>, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import { BaseHttpHandler, HttpHandlerRequest } from "@utils/baseHttp.handler";
import * as superagent from "superagent";

interface IntegrationGetBalanceRequest {
    gameTokenData: any;
    merchantInfo: any;
}

@Injectable()
export class BalanceHttpHandler extends BaseHttpHandler
    implements HttpHandler<IntegrationGetBalanceRequest | IntegrationPaymentRequest, Balance> {

    public async build(req: IntegrationGetBalanceRequest | IntegrationPaymentRequest): Promise<HTTPOperatorRequest> {
        const gameTokenData = this.getGameTokenData(req);

        this.validateRequiredFields(gameTokenData, ["customer", "token"]);

        const customer = this.extractCustomerFromToken(gameTokenData);
        const token = this.extractTokenFromGameData(gameTokenData);

        const authRequest: OperatorAuthRequest = this.buildOperatorRequest(customer, token);

        return this.buildHttpRequest({
            endpoint: "auth",
            method: "post",
            payload: authRequest,
            merchantInfo: req.merchantInfo,
            retryAvailable: true
        } as HttpHandlerRequest);
    }

    public async parse(response: superagent.Response): Promise<Balance> {
        const operatorResponse = this.parseHttpResponse<OperatorAuthResponse>(response);

        return {
            main: this.sanitizeAmount(operatorResponse.balance + (operatorResponse.bonusBalance || 0))
        };
    }
}
