import { Controller, Get, Query, UseFilters } from "@nestjs/common";
import { ValidationPipe } from "@nestjs/common";
import { HistoryService } from "@operator/services/history.service";
import { OperatorHistoryRequest } from "@entities/operator.entities";
import { ErrorFilter } from "@skywind-group/sw-integration-core";

@Controller("history")
export class HistoryController {
    constructor(private historyService: HistoryService) {}

    @Get("/image")
    @UseFilters(ErrorFilter)
    async getGameHistoryImageUrl(
        @Query(new ValidationPipe()) request: OperatorHistoryRequest
    ): Promise<{ imageUrl: string }> {
        const imageUrl = await this.historyService.getGameHistoryImageUrl(request);
        return { imageUrl };
    }

    @Get("/details")
    @UseFilters(ErrorFilter)
    async getGameHistoryDetails(
        @Query(new ValidationPipe()) request: OperatorHistoryRequest
    ): Promise<any> {
        const details = await this.historyService.getGameHistoryDetails(request);
        return details;
    }
}
