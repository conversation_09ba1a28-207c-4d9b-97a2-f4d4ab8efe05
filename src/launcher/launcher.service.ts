import { Injectable, Inject } from "@nestjs/common";
import { BaseHttpService, PlayMode } from "@skywind-group/sw-wallet-adapter-core";
import { IntegrationGameLaunchRequest, IntegrationStartGameTokenData } from "@entities/operator.entities";
import { ValidationError } from "@errors/operator.errors";
import { Names } from "@names";
import { logging, measures } from "@skywind-group/sw-utils";
import * as jwt from "jsonwebtoken";
import {
    buildGameUrl,
    createDemoGameParams,
    createRealGameParams,
    sanitizeGameId,
    sanitizeTableId,
    parsePlatformType
} from "@utils/operator.utils";
import config from "@config";

const log = logging.logger("LauncherService");
const { measure } = measures;

@Injectable()
export class LauncherService {
    public static FALCON_GAME_URL = "v1/merchants/game/url";

    constructor(@Inject(Names.BaseHttpService) private baseHttpService: BaseHttpService) {}

    @measure({ name: "LauncherService.getGameUrl", isAsync: true })
    async getGameUrl(data: IntegrationGameLaunchRequest, ip: string): Promise<string> {
        this.validateRequest(data);
        
        const sanitizedData = this.sanitizeData(data);
        const options = this.mapToSW(sanitizedData, ip);
        
        const { token, url } = await this.baseHttpService.post<any>(
            LauncherService.FALCON_GAME_URL, 
            options
        );
        
        if (token) {
            const startGameToken: IntegrationStartGameTokenData = this.parseToken(token);
            if (startGameToken.loginFailed) {
                return startGameToken.relaunchUrl;
            }
        }

        return url;
    }

    @measure({ name: "LauncherService.buildOperatorGameUrl", isAsync: false })
    buildOperatorGameUrl(data: IntegrationGameLaunchRequest): string {
        this.validateRequest(data);
        
        const sanitizedData = this.sanitizeData(data);
        const baseUrl = config.http.operatorUrl;
        
        let gameParams: any;
        
        if (sanitizedData.demo) {
            gameParams = createDemoGameParams(
                sanitizedData.gameId,
                sanitizedData.lang,
                sanitizedData.platform,
                sanitizedData.trader,
                sanitizedData.lobby,
                sanitizedData.tableId
            );
        } else {
            if (!sanitizedData.customer || !sanitizedData.token) {
                throw new ValidationError("Customer and token are required for real money games");
            }
            
            gameParams = createRealGameParams(
                sanitizedData.currency,
                sanitizedData.customer,
                sanitizedData.gameId,
                sanitizedData.lang,
                sanitizedData.platform,
                sanitizedData.token,
                sanitizedData.trader,
                sanitizedData.country,
                sanitizedData.lobby,
                sanitizedData.tableId
            );
        }
        
        return buildGameUrl(`${baseUrl}/casino-engine/game`, gameParams);
    }

    private validateRequest(data: IntegrationGameLaunchRequest): void {
        if (!data.gameId || data.gameId.trim() === "") {
            throw new ValidationError("Game ID is required");
        }
        
        if (!data.lang || data.lang.trim() === "") {
            throw new ValidationError("Language is required");
        }
        
        if (!data.trader || data.trader.trim() === "") {
            throw new ValidationError("Trader is required");
        }
        
        if (!data.demo && (!data.customer || !data.token)) {
            throw new ValidationError("Customer and token are required for real money games");
        }
        
        if (!data.demo && (!data.currency || data.currency.trim() === "")) {
            throw new ValidationError("Currency is required for real money games");
        }
        
        // Validate platform
        if (!["d", "m", "desktop", "mobile"].includes(data.platform)) {
            throw new ValidationError("Invalid platform. Must be 'd', 'm', 'desktop', or 'mobile'");
        }
    }

    private sanitizeData(data: IntegrationGameLaunchRequest): IntegrationGameLaunchRequest {
        return {
            ...data,
            gameId: sanitizeGameId(data.gameId),
            lang: data.lang.toLowerCase(),
            currency: data.currency ? data.currency.toUpperCase() : "FUN",
            platform: parsePlatformType(data.platform) as any,
            tableId: sanitizeTableId(data.tableId),
            trader: data.trader.trim(),
            lobby: data.lobby?.trim(),
            country: data.country?.toUpperCase(),
            customer: data.customer?.trim(),
            token: data.token?.trim()
        };
    }

    private mapToSW(data: IntegrationGameLaunchRequest, ip: string): any {
        const playMode = data.demo ? PlayMode.FUN : PlayMode.REAL;
        
        return {
            gameCode: data.gameId,
            playMode: playMode,
            publicToken: data.token,
            language: data.lang,
            currency: data.currency,
            platform: data.platform === "d" ? "desktop" : "mobile",
            playerIp: ip,
            merchantCode: config.merchantCode,
            merchantType: config.merchantType,
            // operator-specific data
            trader: data.trader,
            tableId: data.tableId,
            lobby: data.lobby,
            country: data.country,
            demo: data.demo,
            customer: data.customer
        };
    }

    private parseToken(token: string): IntegrationStartGameTokenData {
        try {
            return jwt.decode(token) as IntegrationStartGameTokenData;
        } catch (err) {
            log.error(err, "Failed to parse game token");
            throw new ValidationError("Invalid game token");
        }
    }
}
