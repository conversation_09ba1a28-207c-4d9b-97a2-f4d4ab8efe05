import { Modu<PERSON> } from "@nestjs/common";
import { LauncherController } from "./launcher.controller";
import { LauncherService } from "./launcher.service";
import { BaseHttpService } from "@skywind-group/sw-wallet-adapter-core";
import config from "@config";
import { Names } from "@names";

@Module({
    controllers: [LauncherController],
    providers: [
        LauncherService,
        {
            provide: Names.BaseHttpService,
            useFactory: () => {
                return new BaseHttpService(config.http.operatorUrl);
            }
        }
    ]
})
export class LauncherModule {}
