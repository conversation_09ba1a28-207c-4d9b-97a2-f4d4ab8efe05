import { Controller, Get, Query, UseFilters, Redirect, HttpStatus } from "@nestjs/common";
import { ValidationPipe } from "@nestjs/common";
import { LauncherService } from "./launcher.service";
import { IntegrationGameLaunchRequest } from "@entities/operator.entities";
import { ErrorFilter } from "@skywind-group/sw-integration-core";
import { ClientIp } from "@utils/clientIp.decorator";

@Controller("game")
export class LauncherController {
    constructor(private launcherService: LauncherService) {}

    @Get("/url")
    @UseFilters(ErrorFilter)
    @Redirect(undefined, HttpStatus.FOUND)
    async getGameUrl(
        @Query(new ValidationPipe()) data: IntegrationGameLaunchRequest,
        @ClientIp() ip: string
    ): Promise<{ url: string }> {
        const url = await this.launcherService.getGameUrl(data, ip);
        return { url };
    }

    @Get("/url/noredirect")
    @UseFilters(ErrorFilter)
    async getGameUrlWithoutRedirection(
        @Query(new ValidationPipe()) data: IntegrationGameLaunchRequest,
        @ClientIp() ip: string
    ): Promise<{ url: string }> {
        const url = await this.launcherService.getGameUrl(data, ip);
        return { url };
    }

    @Get("/operator/url")
    @UseFilters(ErrorFilter)
    @Redirect(undefined, HttpStatus.FOUND)
    async getOperatorGameUrl(
        @Query(new ValidationPipe()) data: IntegrationGameLaunchRequest
    ): Promise<{ url: string }> {
        const url = this.launcherService.buildOperatorGameUrl(data);
        return { url };
    }

    @Get("/operator/url/noredirect")
    @UseFilters(ErrorFilter)
    async getOperatorGameUrlWithoutRedirection(
        @Query(new ValidationPipe()) data: IntegrationGameLaunchRequest
    ): Promise<{ url: string }> {
        const url = this.launcherService.buildOperatorGameUrl(data);
        return { url };
    }
}
