import { HttpGatewayConfig } from "@skywind-group/sw-integration-core";

const http: HttpGatewayConfig = {
    operatorUrl: process.env.OPERATOR_BASE_ENGINE_URL || "http://casinoengine.test.operatorgaming.com",
    defaultOptions: {
        timeout: +process.env.OPERATOR_HTTP_TIMEOUT || 10000,
        proxy: process.env.OPERATOR_HTTP_PROXY
    },
    keepAlive: {
        maxFreeSockets: +process.env.OPERATOR_HTTP_KEEP_ALIVE_MAX_FREE_SOCKETS || 100,
        socketActiveTTL: +process.env.OPERATOR_HTTP_KEEP_ALIVE_SOCKET_TTL || 60000,
        freeSocketKeepAliveTimeout: +process.env.OPERATOR_HTTP_KEEP_ALIVE_TIMEOUT || 30000
    },
    ssl: {
        ca: process.env.OPERATOR_SSL_CA,
        key: process.env.OPERATOR_SSL_KEY,
        cert: process.env.OPERATOR_SSL_CERT,
        password: process.env.OPERATOR_SSL_PASSWORD
    }
};

const config = {
    environment: process.env.NODE_ENV || "development",
    
    isProduction: (): boolean => {
        return config.environment === "production";
    },

    server: {
        walletPort: +process.env.SERVER_WALLET_PORT || 3000,
        launcherPort: +process.env.SERVER_LAUNCHER_PORT || 3001,
        operatorPort: +process.env.SERVER_OPERATOR_PORT || 3002,
        mockPort: +process.env.SERVER_MOCK_PORT || 3003
    },

    internalServer: {
        port: +process.env.INTERNAL_SERVER_PORT || 4054,
        api: {
            isEnabled: process.env.INTERNAL_API === "true"
        }
    },

    logging: {
        logLevel: process.env.LOG_LEVEL || "info",
        loggingOutput: (process.env.LOGGING_OUTPUT_TYPE || "console") as any,
        rootLogger: process.env.LOGGING_ROOT_LOGGER || "sw-integration-api",
        defaultSecureKeys: [
            "password", "newPassword", "key", "token", "accessToken", 
            "secretKey", "privateToken", "sharedKey", "genericSecretKey"
        ]
    },

    // operator-specific configuration
    operator: {
        vendorCode: process.env.OPERATOR_VENDOR_CODE || "znidi_gaming",
        genericId: process.env.OPERATOR_GENERIC_ID || "46b0da0cd81423dcdac17d2070b4af16",
        genericSecretKey: process.env.OPERATOR_GENERIC_SECRET_KEY || "86b04d46bb0e81a1131c6e6acd2b7e75",
        apiVersion: "v5",
        tokenExpirationMinutes: +process.env.OPERATOR_TOKEN_EXPIRATION_MINUTES || 5,
        roundDetailsUrl: process.env.OPERATOR_ROUND_DETAILS_URL || "https://api.provider.com/round-details",
        freespinApiUrl: process.env.OPERATOR_FREESPIN_API_URL || "https://api.provider.com/freespins"
    },

    merchantType: process.env.MERCHANT_TYPE || "operator",
    merchantCode: process.env.MERCHANT_CODE || "operator",
    defaultPlayerIp: process.env.DEFAULT_PLAYER_IP || "127.0.0.1",
    defaultJurisdiction: process.env.DEFAULT_JURISDICTION || "UK",
    
    currencyUnitMultiplier: +process.env.CURRENCY_UNIT_MULTIPLIER || 100,
    messageIdNumberLength: +process.env.MESSAGE_ID_NUMBER_LENGTH || 9,
    
    securedKeys: ["password", "username", "privateToken", "sharedKey", "genericSecretKey"],

    http
};

export default config;
