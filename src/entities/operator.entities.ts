import { MerchantGameInitRequest, MerchantGameTokenData, MerchantStartGameTokenData, PaymentRequest } from "@skywind-group/sw-wallet-adapter-core";
import { CommitBonusPaymentRequest } from "@skywind-group/sw-integration-core";

export interface IntegrationGameLaunchRequest {
    gameId: string;
    lang: string;
    trader: string;
    platform: string;
    demo?: boolean;
    currency?: string;
    customer?: string;
    token?: string;
    tableId?: string;
    lobby?: string;
    country?: string;
}

export interface IntegrationGameTokenData extends MerchantGameTokenData {
    customer: string;
    token: string;
    gameId: string;
    currency: string;
    demo: boolean;
    platform: string;
    lang?: string;
    lobby?: string;
    tableId?: string;
    trader?: string;
    country?: string;
    language?: string;
}

export interface IntegrationStartGameTokenData extends MerchantStartGameTokenData {
    customer: string;
    token: string;
    gameId: string;
    currency: string;
    demo: boolean;
    platform: string;
    lang?: string;
    lobby?: string;
    tableId?: string;
    trader?: string;
    country?: string;
    relaunchUrl?: string;
    loginFailed?: boolean;
}

export interface IntegrationInitRequest extends MerchantGameInitRequest {
    customer: string;
    token: string;
    gameId: string;
    currency: string;
    demo: boolean;
    platform: string;
    lang?: string;
    lobby?: string;
    tableId?: string;
    trader?: string;
    country?: string;
}

export interface IntegrationPaymentRequest extends CommitBonusPaymentRequest<IntegrationGameTokenData> {
    request: PaymentRequest;
    operatorRoundId?: string;
}

export interface OperatorFreespinData {
    freespinRef: string;
    requested?: boolean;
    remainingRounds?: number;
    totalWinnings?: number;
}

export interface OperatorPromoData {
    promoType: OperatorPromoType;
    promoRef: string;
    freeSpinData?: OperatorFreespinData;
}

export enum OperatorPromoType {
    FSW = "FSW", // freespin
    JPW = "JPW", // jackpot
    CB = "CB",   // cashBack
    TW = "TW",   // tournament win
    RW = "RW",   // reward
    REW = "REW", // red envelope win
    CDW = "CDW", // cash drop win
    RB = "RB"    // rakeBack
}

export interface OperatorBaseRequest {
    customer: string;
    token: string;
    hash?: string;
}

export interface OperatorAuthRequest extends OperatorBaseRequest {
}

export interface OperatorDebitRequest extends OperatorBaseRequest {
    gameId: string;
    amount: number;
    currency: string;
    betId: string;
    trxId: string;
    tip?: boolean;
}

export interface OperatorCreditRequest extends OperatorBaseRequest {
    gameId: string;
    amount: number;
    currency: string;
    betId: string;
    trxId: string;
    freespin?: OperatorFreespinData;
}

export interface OperatorDebitCreditRequest extends OperatorBaseRequest {
    gameId: string;
    amount: number;
    creditAmount: number;
    currency: string;
    betId: string;
    trxId: string;
    creditTrxId: string;
    tip?: boolean;
}

export interface OperatorRollbackRequest extends OperatorBaseRequest {
    gameId: string;
    trxId: string;
}

export interface OperatorPromoRequest extends OperatorBaseRequest {
    gameId: string;
    amount: number;
    currency: string;
    betId: string;
    trxId: string;
    promo: OperatorPromoData;
}

export interface OperatorBaseResponse {
    code: number;
    status: string;
    currency: string;
    balance: number;
    bonusBalance: number;
}

export interface OperatorAuthResponse extends OperatorBaseResponse {
    traderId?: number;
}

export interface OperatorDebitResponse extends OperatorBaseResponse {
    trxId: number;
}

export interface OperatorCreditResponse extends OperatorBaseResponse {
    trxId?: number;
}

export interface OperatorDebitCreditResponse extends OperatorBaseResponse {
    trxId: number;
    creditTrxId?: number;
}

export interface OperatorRollbackResponse extends OperatorBaseResponse {
    trxId: number;
}

export interface OperatorPromoResponse extends OperatorBaseResponse {
    trxId: number;
}

export interface OperatorHistoryRequest {
    customer: string;
    roundId: string;
    gameId: string;
    lang?: string;
    trader?: string;
}

export interface OperatorRoundDetailsResponse {
    code: number;
    status: string;
    json?: any;
    image?: {
        url: string;
        width?: number;
        height?: number;
    };
    html?: string;
}
