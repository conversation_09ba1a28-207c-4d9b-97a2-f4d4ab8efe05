# SW Integration Documentation

This directory contains comprehensive documentation for building integration systems based on the SW Integration architecture patterns, specifically implemented for the PCES Generic Vendor API v5.

## 📚 Documentation Index

### 1. [SW Integration Architecture Guide](SW_INTEGRATION_ARCHITECTURE_GUIDE.md)
**Comprehensive architectural documentation covering:**
- Multi-service architecture overview (Wallet, Launcher, Operator services)
- Project structure and organization patterns
- Core components and implementation patterns
- Configuration management strategies
- Error handling approaches and best practices
- Testing methodologies and frameworks
- Database integration with TypeORM
- HTTP communication patterns
- Deployment and scaling considerations
- Implementation checklist and best practices

### 2. [SW Integration Implementation Templates](SW_INTEGRATION_IMPLEMENTATION_TEMPLATES.md)
**Ready-to-use code templates including:**
- Complete project setup (package.json, tsconfig.json, nest-cli.json)
- Main service entry points (Wallet, Launcher, Operator services)
- Configuration templates with environment variables
- Entity definitions for integration and operator data
- HTTP handler templates for API communication
- Base classes and utility functions

### 3. [SW Integration Service Templates](SW_INTEGRATION_SERVICE_TEMPLATES.md)
**Service layer implementation templates:**
- Payment Service with comprehensive business logic
- Round Service for game round management
- Launcher Service for game URL generation
- Module configurations (Wallet, Launcher, Operator, Database)
- Controller templates for HTTP endpoints
- Utility functions and helper classes
- Error mapping implementations
- Testing frameworks and mock implementations

### 4. [PCES Generic Vendor API v5 Documentation](PCES-Generic%20Vendor%20API%20v5-120525-143606.md)
**Original PCES API specification including:**
- Complete API endpoint documentation
- Request/response schemas
- Authentication and security requirements
- Error codes and handling
- Hash validation procedures
- Promotional features support
- Integration examples and best practices

## 🏗️ Architecture Overview

The SW Integration system follows a **microservices architecture** with these key characteristics:

### Core Services
- **Wallet Service** - Handles payments, balance management, and financial transactions
- **Launcher Service** - Manages game URL generation and game launching  
- **Operator Service** - Handles operator-specific integrations and communications

### Key Patterns
- **HTTP Handler Pattern** - Encapsulates request/response logic for external API calls
- **Service Layer Pattern** - Contains business logic and orchestrates handlers
- **Repository Pattern** - Abstracts database operations
- **Error Mapping Pattern** - Systematic mapping between operator and system errors
- **Configuration-Driven Design** - Environment-based configuration management

### Technology Stack
- **Framework**: NestJS with TypeScript
- **Database**: PostgreSQL with TypeORM
- **HTTP Client**: Superagent
- **Testing**: Mocha with TypeScript decorators
- **Validation**: class-validator
- **Logging**: Structured logging with @skywind-group/sw-utils

## 🚀 Quick Start

1. **Read the Architecture Guide** - Start with understanding the overall architecture and patterns
2. **Review Implementation Templates** - Use the templates as starting points for your integration
3. **Study Service Templates** - Understand the service layer implementations
4. **Check PCES API Documentation** - Familiarize yourself with the specific API requirements
5. **Follow the Implementation Checklist** - Use the checklist in the Architecture Guide

## 🎯 PCES Integration Specifics

### API Endpoints Implemented
- `/auth` - Authentication and balance retrieval
- `/debit` - Bet placement (debit player balance)
- `/credit` - Win settlement (credit player balance)
- `/debit-credit` - Combined bet and settlement
- `/rollback` - Transaction reversal
- `/promo` - Promotional wins (freespins, jackpots, etc.)

### Security Features
- **HMAC-SHA256 Hash Validation** for all requests
- **Generic-Id Header Authentication**
- **Token-based Player Authentication**
- **Input Validation** with comprehensive error handling

### Promotional Support
- **FSW**: Freespin wins
- **JPW**: Jackpot wins
- **CB**: Cashback
- **TW**: Tournament wins
- **RW**: Rewards
- **REW**: Red envelope wins
- **CDW**: Cash drop wins
- **RB**: Rakeback

## 📋 Implementation Checklist

### Initial Setup
- [ ] Set up project structure with proper module organization
- [ ] Configure TypeScript with appropriate compiler options
- [ ] Set up NestJS with required dependencies
- [ ] Configure path aliases for clean imports
- [ ] Set up testing framework (Mocha)

### Core Components
- [ ] Implement bootstrap system for service startup
- [ ] Create base HTTP handler classes
- [ ] Implement HTTP gateway for centralized communication
- [ ] Set up error hierarchy and error mapping
- [ ] Create entity definitions with proper validation

### PCES-Specific Implementation
- [ ] Implement PCES authentication handler
- [ ] Create transaction handlers (debit, credit, debit-credit)
- [ ] Implement rollback functionality
- [ ] Add promotional wins support
- [ ] Set up hash validation for security

### Services Implementation
- [ ] Implement wallet service for payment operations
- [ ] Implement launcher service for game URL generation
- [ ] Implement operator service for history and communications
- [ ] Create comprehensive payment service with all transaction types

### Testing & Quality
- [ ] Create unit tests for all services
- [ ] Implement integration tests
- [ ] Set up mock system for external dependencies
- [ ] Create test data factories
- [ ] Add error scenario testing

### Configuration & Deployment
- [ ] Set up environment-based configuration
- [ ] Configure PCES-specific settings
- [ ] Set up health check endpoints
- [ ] Configure logging and monitoring
- [ ] Create deployment documentation

## 🔧 Configuration

Key environment variables for pronet integration:

```bash
# PRONET API Configuration
PRONET_BASE_ENGINE_URL=http://casinoengine.test.pronetgaming.com
PRONET_VENDOR_CODE=znidi_gaming
PRONET_GENERIC_ID=46b0da0cd81423dcdac17d2070b4af16
PRONET_GENERIC_SECRET_KEY=86b04d46bb0e81a1131c6e6acd2b7e75

# Service Ports
SERVER_WALLET_PORT=3000
SERVER_LAUNCHER_PORT=3001
SERVER_OPERATOR_PORT=3002
```

## 🤝 Contributing

When implementing a new integration based on these guidelines:

1. Follow the established patterns and conventions
2. Use the provided templates as starting points
3. Implement comprehensive tests
4. Document any operator-specific requirements
5. Follow the implementation checklist

## 📞 Support

For questions about the architecture or implementation:
- Review the comprehensive guides in this documentation
- Check the implementation templates for code examples
- Refer to the PCES API documentation for operator-specific details
- Use the testing patterns for validation

---

This documentation provides everything needed to build robust, scalable integration systems following proven architectural patterns.
