# Environment Configuration
NODE_ENV=development

# Server Configuration
SERVER_WALLET_PORT=3000
SERVER_LAUNCHER_PORT=3001
SERVER_OPERATOR_PORT=3002
SERVER_MOCK_PORT=3003
INTERNAL_SERVER_PORT=4054

# Database Configuration
PGDATABASE=management
PGUSER=postgres
PGPASSWORD=password
PGHOST=localhost
PGPORT=5432
PG_SECURE_CONNECTION=false
PG_MAX_CONNECTIONS=10
PG_MAX_IDLE_TIME_MS=30000
PGSCHEMA=public
SYNC_ON_START=false
PG_MASTER_LOGGING=false
PG_MASTER_CACHE=false
PG_MASTER_CACHE_TTL=30000
PG_MASTER_CONNECTION_TIMEOUT_MS=10000

# OPERATOR API Configuration
OPERATOR_BASE_ENGINE_URL=http://casinoengine.test.pronetgaming.com
OPERATOR_VENDOR_CODE=znidi_gaming
OPERATOR_GENERIC_ID=46b0da0cd81423dcdac17d2070b4af16
OPERATOR_GENERIC_SECRET_KEY=86b04d46bb0e81a1131c6e6acd2b7e75
OPERATOR_HTTP_TIMEOUT=10000
OPERATOR_TOKEN_EXPIRATION_MINUTES=5
OPERATOR_ROUND_DETAILS_URL=https://api.provider.com/round-details
OPERATOR_FREESPIN_API_URL=https://api.provider.com/freespins

# HTTP Configuration
OPERATOR_HTTP_PROXY=
OPERATOR_HTTP_KEEP_ALIVE_MAX_FREE_SOCKETS=100
OPERATOR_HTTP_KEEP_ALIVE_SOCKET_TTL=60000
OPERATOR_HTTP_KEEP_ALIVE_TIMEOUT=30000

# SSL Configuration (optional)
OPERATOR_SSL_CA=
OPERATOR_SSL_KEY=
OPERATOR_SSL_CERT=
OPERATOR_SSL_PASSWORD=

# Logging Configuration
LOG_LEVEL=info
LOGGING_OUTPUT_TYPE=console
LOGGING_ROOT_LOGGER=sw-integration-api

# Merchant Configuration
MERCHANT_TYPE=pronet
MERCHANT_CODE=pronet
DEFAULT_PLAYER_IP=127.0.0.1
DEFAULT_JURISDICTION=UK

# Currency Configuration
CURRENCY_UNIT_MULTIPLIER=100

# Internal API
INTERNAL_API=false

# Measures Provider
MEASURES_PROVIDER=prometheus
